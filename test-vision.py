from PIL import Image
import io
import asyncio
from datetime import datetime
import ollama
import base64

client = ollama.AsyncClient()

def preprocess_image(path, size=(256, 256)):
    img = Image.open(path).convert("RGB")
    img = img.resize(size)
    buffer = io.BytesIO()
    img.save(buffer, format="PNG")
    encoded = base64.b64encode(buffer.getvalue()).decode("utf-8")
    return encoded

async def main():
    res = await client.chat(
        model='gemma3n:e4b',
        messages=[
            {
                'role': 'user',
                'content': 'What is in this image?',
                'images': [preprocess_image('./g.png')]
            }
        ],
        keep_alive=-1
    )

    print(res['message']['content'])

if __name__ == "__main__":
  try:
    #start time counter
    start_time = datetime.now()
  
    #run main program and check for ctrl-break
    asyncio.run(main())

    #stop timer and print elasped time
    end_time = datetime.now()
    print("⏱️ Elapsed time:", round((end_time - start_time).total_seconds(), 2), "sec\n")
  except KeyboardInterrupt:
    print("\nGoodbye!")
    
    #llava:13b-v1.6-vicuna-q4_K_M: 17.51 (56%)
    #llava:7b-v1.6-mistral-q5_K_M: 5.24 (100%)
    #llava-phi3:3.8b-mini-q4_0: 2.11 (100%)
    #Gapsar/minicpm-o_2.6 5.44 (92%)
    #qwen2.5vl:3b 2.46 (6.4 100%)
    #moondream:1.8b-v2-q4_K_M 0.87 (4.6 100%)
    #moondream:1.8b-v2-q8_0 1.32 (5.2 100%)
   
