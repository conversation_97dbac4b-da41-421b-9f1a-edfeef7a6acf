{"schemaVersion": 2, "mediaType": "application/vnd.docker.distribution.manifest.v2+json", "config": {"mediaType": "application/vnd.docker.container.image.v1+json", "digest": "sha256:d66f736273086b63684ff6ad2c11414138424bd5cf15a211d8c72ab1d79ff1ce", "size": 413}, "layers": [{"mediaType": "application/vnd.ollama.image.model", "digest": "sha256:1f5adbe8cb0a6400f51abdca3bf4e32284ebff73cc681a43abb35c0a6ccd3820", "size": 4683071008, "from": "F:\\AI\\Ollama\\models\\blobs\\sha256-1f5adbe8cb0a6400f51abdca3bf4e32284ebff73cc681a43abb35c0a6ccd3820"}, {"mediaType": "application/vnd.ollama.image.template", "digest": "sha256:974fd507768e24f5aba85a1349d1708b1832a06228c38e83afdd8891451e365b", "size": 1430}, {"mediaType": "application/vnd.ollama.image.params", "digest": "sha256:f02dd72bb2423204352eabc5637b44d79d17f109fdb510a7c51455892aa2d216", "size": 59}]}