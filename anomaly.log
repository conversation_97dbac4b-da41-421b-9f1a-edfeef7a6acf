2025-07-03 00:33:58 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='▍▋▊▌▎▎▋▊...▌▉ ▎▋▉ ▍  ', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-03 00:34:45 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='▋▋▌▍▌▎▋▍...▊ ▌▏ ▌▏ ▎', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-03 00:35:47 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='▏▎ ▌ ▊▌▉▊... ▏▎▌▊▌▍▊ ', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-04 23:11:50 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='▋▉ ▊ ▌ ▌▌▉...▌▌▌▎▍▋▊', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-04 23:27:13 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='▉▉▌▏▋▊▉▏...▍▌▏▍▌▌▋ ', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-04 23:29:35 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value=' ▍▌ ▎▊▋   ▊...▎▋▌▎▎▋▎', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-04 23:32:12 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='▎▌▌▉▉▎▎▉...▋▉  ▋▊▍ ▎', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-04 23:33:39 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='▎▉▊▋▊  ▉▎...▊▍▋▊▊▉▌', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-04 23:35:38 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='▊ ▊▌▎▎▊▏...▏▌▍▉ ▋▏▌', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-04 23:38:18 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='▍▌ ▊▋ ▋▌▌...▍▉▌ ▌▊▉ ▊', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-04 23:45:19 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='▉▋ ▎▍▊▊▌...▍▊▎▏▊▎  ▎', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-04 23:46:51 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='  ▎▉▏ ▌▊ ▊ ...▏▎▋▌▊▊▋ ', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-05 00:23:27 [ERROR] maximum recursion depth exceeded
2025-07-05 00:24:52 [ERROR] maximum recursion depth exceeded
2025-07-05 00:25:38 [ERROR] maximum recursion depth exceeded
2025-07-05 12:41:59 [ERROR] MAC check failed
2025-07-05 12:42:57 [ERROR] MAC check failed
2025-07-05 12:44:06 [ERROR] MAC check failed
2025-07-05 12:46:32 [ERROR] MAC check failed
2025-07-05 12:52:33 [ERROR] MAC check failed
2025-07-05 14:22:32 [ERROR] division by zero
2025-07-05 14:24:35 [ERROR] division by zero
2025-07-05 14:28:22 [ERROR] division by zero
2025-07-05 14:29:09 [ERROR] division by zero
2025-07-05 14:38:09 [ERROR] division by zero
2025-07-05 14:39:08 [ERROR] division by zero
2025-07-05 14:42:23 [ERROR] division by zero
2025-07-05 14:49:25 [ERROR] division by zero
2025-07-05 14:51:25 [ERROR] division by zero
2025-07-05 15:14:08 [ERROR] object NoneType can't be used in 'await' expression
2025-07-05 15:35:40 [ERROR] Validation failed: 1 validation error for AddStimulus
numbers
  Field required [type=missing, input_value={'a': 123, 'b': 45, 'c': 77, 'd': 13}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-07-05 15:36:58 [ERROR] Validation failed: 1 validation error for AddStimulus
numbers
  Field required [type=missing, input_value={'a': 123, 'b': 45, 'c': 77, 'd': 13}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-07-05 15:38:41 [ERROR] Validation failed: 1 validation error for AddStimulus
numbers
  Field required [type=missing, input_value={'a': 123, 'b': 45, 'c': 77, 'd': 13}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-07-05 15:40:25 [ERROR] Validation failed: 1 validation error for AddStimulus
numbers
  Field required [type=missing, input_value={'a': 123, 'b': 45, 'c': 77, 'd': 13}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-07-05 15:40:25 [ERROR] Validation failed: 1 validation error for AddStimulus
numbers
  Field required [type=missing, input_value={'a': 123, 'b': 45, 'c': 77, 'd': 13}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-07-05 15:41:20 [ERROR] Validation failed: 1 validation error for AddStimulus
numbers
  Field required [type=missing, input_value={'a': 123, 'b': 45, 'c': 77, 'd': 13}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-07-05 15:41:20 [ERROR] Validation failed: 1 validation error for AddStimulus
numbers
  Field required [type=missing, input_value={'a': 123, 'b': 45, 'c': 77, 'd': 13}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-07-05 15:41:41 [ERROR] Validation failed: 1 validation error for AddStimulus
numbers
  Field required [type=missing, input_value={'a': 123, 'b': 45, 'c': 77, 'd': 13}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-07-05 15:41:41 [ERROR] Validation failed: 1 validation error for AddStimulus
numbers
  Field required [type=missing, input_value={'a': 123, 'b': 45, 'c': 77, 'd': 13}, input_type=dict]
    For further information visit https://errors.pydantic.dev/2.10/v/missing
2025-07-06 16:57:54 [ERROR] Invalid spike symbol in train: 'numbers'
2025-07-06 16:57:55 [ERROR] object Synapse can't be used in 'await' expression
2025-07-06 16:58:21 [ERROR] Invalid spike symbol in train: 'numbers'
2025-07-06 16:58:22 [ERROR] object Synapse can't be used in 'await' expression
2025-07-06 17:04:17 [ERROR] Invalid spike symbol in train: 'numbers'
2025-07-06 21:50:28 [ERROR] object NoneType can't be used in 'await' expression
2025-07-06 21:54:49 [ERROR] object NoneType can't be used in 'await' expression
2025-07-06 21:56:43 [ERROR] object NoneType can't be used in 'await' expression
2025-07-06 21:58:01 [ERROR] object NoneType can't be used in 'await' expression
2025-07-06 22:05:49 [ERROR] object NoneType can't be used in 'await' expression
2025-07-06 23:10:23 [ERROR] asyncio.run() cannot be called from a running event loop
2025-07-06 23:10:52 [ERROR] asyncio.run() cannot be called from a running event loop
2025-07-06 23:11:08 [ERROR] asyncio.run() cannot be called from a running event loop
2025-07-06 23:13:11 [ERROR] asyncio.run() cannot be called from a running event loop
2025-07-06 23:13:42 [ERROR] asyncio.run() cannot be called from a running event loop
2025-07-06 23:17:21 [ERROR] asyncio.run() cannot be called from a running event loop
2025-07-06 23:45:33 [ERROR] object Synapse can't be used in 'await' expression
2025-07-07 00:00:56 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='./grace.txt', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 00:00:57 [ERROR] expected str, bytes or os.PathLike object, not dict
2025-07-07 00:02:08 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='./grace.txt', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 00:02:08 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='./grace.txt', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 00:02:24 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='./grace.txt', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 00:02:24 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='./grace.txt', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 00:03:26 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='J:/grace.txt', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 00:03:26 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='J:/grace.txt', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 00:12:27 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='Y:/Photos/2012/QQ Photos/IMG_1875.JPG', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 00:12:40 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='Y:/Photos/2012/QQ Photos/IMG_1875.JPG', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 00:13:45 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='Y:/Photos/2012/QQ Photos/IMG_1875.JPG', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 00:14:05 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='Y:/Photos/2012/QQ Photos/IMG_1875.JPG', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 00:19:01 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='Y:/Photos/2012/QQ Photos/IMG_1875.JPG', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 00:19:20 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='Y:/Photos/2012/QQ Photos/IMG_1875.JPG', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 00:19:39 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='Y:/Photos/2012/QQ Photos/IMG_1875.JPG', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 00:19:39 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='Y:/Photos/2012/QQ Photos/IMG_1875.JPG', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 00:19:39 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='Y:/Photos/2012/QQ Photos/IMG_1875.JPG', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 00:20:29 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='Y:/Photos/2012/QQ Photos/IMG_1875.JPG', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 00:20:40 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='Y:/Photos/2012/QQ Photos/IMG_1875.JPG', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 00:20:52 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='Y:/Photos/2012/QQ Photos/IMG_1875.JPG', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 00:20:52 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='Y:/Photos/2012/QQ Photos/IMG_1875.JPG', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 00:20:52 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='Y:/Photos/2012/QQ Photos/IMG_1875.JPG', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 00:21:28 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='Y:/Photos/2012/QQ Photos/IMG_1875.JPG', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 00:21:40 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='Y:/Photos/2012/QQ Photos/IMG_1875.JPG', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 00:21:51 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='Y:/Photos/2012/QQ Photos/IMG_1875.JPG', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 00:21:51 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='Y:/Photos/2012/QQ Photos/IMG_1875.JPG', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 00:21:51 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='Y:/Photos/2012/QQ Photos/IMG_1875.JPG', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 22:54:45 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='Y:/Photos/2012/QQ Photos/IMG_1875.JPG', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 22:54:56 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='Y:/Photos/2012/QQ Photos/IMG_1875.JPG', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 22:55:08 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='Y:/Photos/2012/QQ Photos/IMG_1875.JPG', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 22:55:08 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='Y:/Photos/2012/QQ Photos/IMG_1875.JPG', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
2025-07-07 22:55:08 [ERROR] Validation failed: 1 validation error for AddStimulus
  Input should be a valid dictionary or instance of AddStimulus [type=model_type, input_value='Y:/Photos/2012/QQ Photos/IMG_1875.JPG', input_type=str]
    For further information visit https://errors.pydantic.dev/2.10/v/model_type
