{"schemaVersion": 2, "mediaType": "application/vnd.docker.distribution.manifest.v2+json", "config": {"mediaType": "application/vnd.docker.container.image.v1+json", "digest": "sha256:97a23b280c2ebb5f368f1d9eef57c1924b234e7f92e2dca7f3f38d960e9331bd", "size": 567}, "layers": [{"mediaType": "application/vnd.ollama.image.model", "digest": "sha256:e9758e589d443f653821b7be9bb9092c1bf7434522b70ec6e83591b1320fdb4d", "size": **********}, {"mediaType": "application/vnd.ollama.image.template", "digest": "sha256:a242d8dfdc8f8c2b0586ee85fba70adb408fb633aba2836fe1b05f2c46631474", "size": 487}, {"mediaType": "application/vnd.ollama.image.system", "digest": "sha256:75357d685f238b6afd7738be9786fdafde641eb6ca9a3be7471939715a68a4de", "size": 28}, {"mediaType": "application/vnd.ollama.image.license", "digest": "sha256:832dd9e00a68dd83b3c3fb9f5588dad7dcf337a0db50f7d9483f310cd292e92e", "size": 11343}, {"mediaType": "application/vnd.ollama.image.params", "digest": "sha256:52d2a7aa3a380c606bd1cd3d6f777a9c65a1c77c2e0cb091eed2968a5ef04dc3", "size": 23}]}