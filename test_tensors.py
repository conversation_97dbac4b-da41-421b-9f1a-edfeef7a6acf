import torch, time, numpy as np
from functools import lru_cache
from sympy import primerange

secret = b"Hello world" * 10000  # make it bigger!
pad = b"\x00" * (len(secret) % 2)
padded = secret + pad 

flag=True

# CPU normal
start = time.perf_counter()
prime_set: set = set(primerange(0, len(secret)))
primes = [i for i in range(len(secret)) if i in prime_set]
non_primes = [i for i in range(len(secret)) if i not in prime_set]

if flag:
    original = bytes(secret[i] for i in primes + non_primes)
else:
    prime_part = secret[:len(primes)]
    non_prime_part = secret[len(primes):]
    original = bytearray(len(secret))
    for idx, i in enumerate(primes):
        original[i] = prime_part[idx]
    for idx, i in enumerate(non_primes):
        original[i] = non_prime_part[idx]                   
cpu_result = bytes(original)
print(f"CPU Time taken: {time.perf_counter() - start:.6f} seconds")

start = time.perf_counter()
length = len(secret)
prime_set = set(primerange(0, length))
indices = np.arange(length)

primes = indices[np.isin(indices, list(prime_set))]
non_primes = indices[~np.isin(indices, list(prime_set))]

arr = np.frombuffer(secret, dtype=np.uint8)

if flag:
    # concatenate prime indices then non-prime indices
    result = np.concatenate((arr[primes], arr[non_primes]))
else:
    prime_part = arr[:len(primes)]
    non_prime_part = arr[len(primes):]
    original = np.empty(length, dtype=np.uint8)
    original[primes] = prime_part
    original[non_primes] = non_prime_part
    result = original

numpy_result = result.tobytes()
print(f"Numpy Time taken: {time.perf_counter() - start:.6f} seconds")

torch.manual_seed(42)
torch.set_num_threads(1)  # Set to 1 to avoid parallelism issues in some environments

# GPU normal
start = time.perf_counter()
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
secret_tensor = torch.tensor(list(secret), dtype=torch.uint8, device=device)
padded_tensor = torch.tensor(list(padded), dtype=torch.uint8, device=device)

# Reshape and reverse
reshaped_tensor = padded_tensor.view(-1, 2).flip(dims=[0])
gpu_result = reshaped_tensor.flatten().cpu().numpy().tobytes()

end = time.perf_counter()
print(f"CUDA Time taken: {end - start:.6f} seconds")

assert cpu_result == gpu_result == numpy_result