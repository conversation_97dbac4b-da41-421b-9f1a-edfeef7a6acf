import torch
import numpy as np
import time
import multiprocessing as mp
from concurrent.futures import Thread<PERSON>ool<PERSON>xecutor, ProcessPoolExecutor
from functools import partial
import hashlib

class IntenseScrambler:
    """
    Intense scrambling process for performance testing between PyTorch and NumPy
    """
    
    def __init__(self, data_size=100000, num_iterations=10):
        self.data_size = data_size
        self.num_iterations = num_iterations
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
    def generate_test_data(self):
        """Generate test data for scrambling"""
        # Create some meaningful data to scramble
        np.random.seed(42)
        data = np.random.randint(0, 256, self.data_size, dtype=np.uint8)
        return data
    
    def numpy_intense_scramble(self, data, iteration=0):
        """Intense scrambling using NumPy operations"""
        arr = data.copy()
        
        # Multiple scrambling operations
        for i in range(self.num_iterations):
            # 1. Bit manipulation scrambling
            arr = arr ^ ((i + iteration) % 256)
            
            # 2. Index-based scrambling with prime numbers
            prime_indices = self._get_prime_indices(len(arr))
            arr[prime_indices] = np.roll(arr[prime_indices], i + 1)
            
            # 3. Matrix reshaping and transposition scrambling
            if len(arr) % 4 == 0:
                temp = arr.reshape(-1, 4)
                temp = np.transpose(temp)
                arr = temp.flatten()
            
            # 4. Circular shifts based on hash
            hash_val = int(hashlib.md5(arr.tobytes()).hexdigest()[:8], 16)
            shift = hash_val % len(arr)
            arr = np.roll(arr, shift)
            
            # 5. Byte swapping in pairs
            if len(arr) % 2 == 0:
                arr = arr.reshape(-1, 2)
                arr = arr[:, ::-1]  # Reverse pairs
                arr = arr.flatten()
            
            # 6. XOR with position-dependent values
            positions = np.arange(len(arr))
            arr = arr ^ (positions % 256).astype(np.uint8)
            
        return arr
    
    def torch_intense_scramble(self, data, iteration=0):
        """Intense scrambling using PyTorch operations"""
        tensor = torch.tensor(data, dtype=torch.uint8, device=self.device)
        
        # Multiple scrambling operations
        for i in range(self.num_iterations):
            # 1. Bit manipulation scrambling
            tensor = tensor ^ ((i + iteration) % 256)
            
            # 2. Index-based scrambling with prime numbers
            prime_indices = self._get_prime_indices_torch(len(tensor))
            if len(prime_indices) > 0:
                tensor[prime_indices] = torch.roll(tensor[prime_indices], i + 1)
            
            # 3. Matrix reshaping and transposition scrambling
            if len(tensor) % 4 == 0:
                temp = tensor.view(-1, 4)
                temp = temp.t()
                tensor = temp.flatten()
            
            # 4. Circular shifts based on hash
            hash_val = int(hashlib.md5(tensor.cpu().numpy().tobytes()).hexdigest()[:8], 16)
            shift = hash_val % len(tensor)
            tensor = torch.roll(tensor, shift)
            
            # 5. Byte swapping in pairs
            if len(tensor) % 2 == 0:
                tensor = tensor.view(-1, 2)
                tensor = torch.flip(tensor, dims=[1])  # Reverse pairs
                tensor = tensor.flatten()
            
            # 6. XOR with position-dependent values
            positions = torch.arange(len(tensor), device=self.device, dtype=torch.uint8)
            tensor = tensor ^ (positions % 256)
            
        return tensor.cpu().numpy() if self.device.type == 'cuda' else tensor.numpy()
    
    def _get_prime_indices(self, n):
        """Get prime number indices up to n using NumPy"""
        if n < 2:
            return np.array([], dtype=int)
        
        sieve = np.ones(n, dtype=bool)
        sieve[0] = sieve[1] = False
        
        for i in range(2, int(n**0.5) + 1):
            if sieve[i]:
                sieve[i*i:n:i] = False
                
        return np.where(sieve)[0]
    
    def _get_prime_indices_torch(self, n):
        """Get prime number indices up to n using PyTorch"""
        # For simplicity, we'll use NumPy for prime generation and convert to torch
        prime_indices = self._get_prime_indices(n)
        return torch.tensor(prime_indices, device=self.device)
    
    def parallel_numpy_scramble(self, data, num_workers=None):
        """Parallel scrambling using NumPy with multiprocessing"""
        if num_workers is None:
            num_workers = mp.cpu_count()
        
        # Split data into chunks
        chunk_size = len(data) // num_workers
        chunks = [data[i:i+chunk_size] for i in range(0, len(data), chunk_size)]
        
        with ProcessPoolExecutor(max_workers=num_workers) as executor:
            scramble_func = partial(self.numpy_intense_scramble)
            results = list(executor.map(scramble_func, chunks, range(len(chunks))))
        
        return np.concatenate(results)
    
    def parallel_torch_scramble(self, data, num_workers=None):
        """Parallel scrambling using PyTorch with threading"""
        if num_workers is None:
            num_workers = mp.cpu_count()
        
        # Split data into chunks
        chunk_size = len(data) // num_workers
        chunks = [data[i:i+chunk_size] for i in range(0, len(data), chunk_size)]
        
        # Use ThreadPoolExecutor for PyTorch (better for GPU operations)
        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            scramble_func = partial(self.torch_intense_scramble)
            results = list(executor.map(scramble_func, chunks, range(len(chunks))))
        
        return np.concatenate(results)
    
    def benchmark_scrambling(self, parallel=False, num_workers=None):
        """Benchmark scrambling performance"""
        print(f"Benchmarking intense scrambling process...")
        print(f"Data size: {self.data_size:,} bytes")
        print(f"Iterations per scramble: {self.num_iterations}")
        print(f"Device: {self.device}")
        print(f"Parallel processing: {parallel}")
        if parallel:
            print(f"Number of workers: {num_workers or mp.cpu_count()}")
        print("-" * 50)
        
        # Generate test data
        test_data = self.generate_test_data()
        
        # NumPy benchmark
        start_time = time.perf_counter()
        if parallel:
            numpy_result = self.parallel_numpy_scramble(test_data, num_workers)
        else:
            numpy_result = self.numpy_intense_scramble(test_data)
        numpy_time = time.perf_counter() - start_time
        
        # PyTorch benchmark
        start_time = time.perf_counter()
        if parallel:
            torch_result = self.parallel_torch_scramble(test_data, num_workers)
        else:
            torch_result = self.torch_intense_scramble(test_data)
        torch_time = time.perf_counter() - start_time
        
        # Results
        print(f"NumPy time: {numpy_time:.6f} seconds")
        print(f"PyTorch time: {torch_time:.6f} seconds")
        print(f"Speedup: {numpy_time/torch_time:.2f}x {'(PyTorch faster)' if torch_time < numpy_time else '(NumPy faster)'}")
        
        # Verify results are different (scrambled)
        print(f"Original data hash: {hashlib.md5(test_data.tobytes()).hexdigest()[:16]}")
        print(f"NumPy result hash: {hashlib.md5(numpy_result.tobytes()).hexdigest()[:16]}")
        print(f"PyTorch result hash: {hashlib.md5(torch_result.tobytes()).hexdigest()[:16]}")
        
        return {
            'numpy_time': numpy_time,
            'torch_time': torch_time,
            'numpy_result': numpy_result,
            'torch_result': torch_result
        }

# Example usage and testing
if __name__ == "__main__":
    # Test with different data sizes
    data_sizes = [10000, 50000, 100000]
    
    for size in data_sizes:
        print(f"\n{'='*60}")
        print(f"Testing with data size: {size:,} bytes")
        print(f"{'='*60}")
        
        scrambler = IntenseScrambler(data_size=size, num_iterations=5)
        
        # Sequential processing
        print("\nSequential Processing:")
        results_seq = scrambler.benchmark_scrambling(parallel=False)
        
        # Parallel processing
        print("\nParallel Processing:")
        results_par = scrambler.benchmark_scrambling(parallel=True, num_workers=4)
        
        print(f"\nParallel vs Sequential Speedup:")
        print(f"NumPy: {results_seq['numpy_time']/results_par['numpy_time']:.2f}x")
        print(f"PyTorch: {results_seq['torch_time']/results_par['torch_time']:.2f}x")