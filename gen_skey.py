# gen_skey.py
# Generate serial number for Amazing GRACE infrastructure

# Import necessary modules and configuration
from datetime import datetime
import hashlib
import random
import string
import uuid

# Define the year and month codes for the serial number
YEAR_CODE_MAP = {
    2020: 'W',
    2021: 'X',
    2022: 'A',
    2023: 'S',
    2024: 'B',
    2025: 'P',
    2026: 'G',
    2027: 'L',
    2028: 'M',
    2029: 'T',
    2030: 'F',
    2031: 'Q',
    2032: 'E',
    2033: 'J',
    2034: 'D',
    2035: 'O',
    2036: 'N',
    2037: 'I',
    2038: 'Y',
    2039: 'H',
    2040: 'U',
    2041: 'V',
    2042: 'R',
    2043: 'K',
    2044: 'Z',
    2045: 'C'
}

# Define the month codes for the serial number
MONTH_CODE_MAP = {
    1: "J",   # January
    2: "F",   # February
    3: "M",   # March
    4: "A",   # April
    5: "Y",   # May
    6: "U",   # June
    7: "L",   # July
    8: "G",   # August
    9: "S",   # September
    10: "O",  # October
    11: "N",  # November
    12: "D",  # December
}

def luhn_checksum(number_str : str) -> int:
    """
    Calculate the <PERSON>hn checksum for the given number string.
    The Luhn algorithm is used to validate credit card numbers and can be applied to generate
    a check digit for a given number string.

    Args:
        number_str (str): The number string to calculate the checksum for.

    Returns:
        int: The Luhn checksum.
    """
    def digits_of(n : str) -> list:
        """
        Convert a string to a list of integers.
        
        Args:
            n (str): The string to convert.

        Returns:
            list: A list of integers.
        """
        return [int(d) for d in n]
    
    # Calculate the Luhn checksum
    digits = digits_of(number_str) # Convert the number string to a list of digits
    odd_sum = sum(digits[-1::-2]) # Sum the odd-positioned digits

    # Sum the even-positioned digits after doubling them
    even_sum = 0
    for d in digits[-2::-2]:
        doubled = d * 2
        even_sum += doubled if doubled < 10 else doubled - 9
    total = odd_sum + even_sum
    return (10 - (total % 10)) % 10 # Return the Luhn checksum

def encode_to_number(s : str) -> str:
    """
    Convert a string to a number string by encoding letters to their position in the alphabet.
    Numbers are kept as is, and other characters are replaced with '00'.

    Args:
        s (str): The input string to encode.

    Returns:
        str: The encoded number string.
    """
    result = ""
    for ch in s:
        if ch.isdigit():
            result += ch
        elif ch.isalpha():
            result += str(ord(ch.upper()) - 55)  # A=10, B=11,...
        else:
            result += '00'  # Other characters are replaced with '00'
    return result

def generate_agi_serial(country_code="124", version="101") -> str:
    """
    Generate a serial number for the Amazing GRACE infrastructure.

    Args:
        country_code (str, optional): The country code for the serial number. Defaults to "124".
        version (str, optional): The version number for the serial number. Defaults to "101".

    Returns:
        str: The generated serial number.
    """

    # Generate the serial number base
    now = datetime.now()
    time_str = f"{now.hour:02}{now.minute:02}{now.second:02}{now.microsecond:06}"
    rand_str = ''.join(random.choices(string.ascii_letters + string.digits, k=8))
    to_hash = time_str + rand_str
    sha = hashlib.sha256(to_hash.encode()).hexdigest()
    unifier = sha[:8].upper()

    # Generate year, month, day codes
    year_code = YEAR_CODE_MAP.get(now.year)
    month_code = MONTH_CODE_MAP.get(now.month)
    day_code = format(now.day, "02X")

    # Combine the base serial without the check digit (all uppercase)
    base_serial = f"AGi{f"{country_code}{year_code}{month_code}{day_code}{unifier}".upper()}"

    # Generate check digit using Luhn algorithm
    number_str = encode_to_number(base_serial)
    check_digit = luhn_checksum(number_str)

    # Combine the base serial with the check digit and version number
    serial = f"{base_serial}{check_digit}/{version}"

    return serial

def serial_to_uuid(serial : str) -> str:
    """
    Convert a serial number to a UUID.

    Args:
        serial (str): The serial number to convert.

    Returns:
        str: The generated UUID.
    """
    # Convert the serial number to bytes
    serial_bytes = serial.encode('utf-8')
    
    # Use the first 16 bytes of the hash to generate the UUID (UUID version 5)
    generated_uuid = uuid.uuid5(uuid.NAMESPACE_DNS, serial_bytes)  # UUID version 5 with SHA-1 hash algorithm (Namespace: DNS)

    # Convert the UUID to a upper case string and add the AGi- prefix
    generated_uuid = f"AGi-{str(generated_uuid).upper()}"
    
    return generated_uuid

def main():
    """
    Main function to generate the serial number.
    """
    
    # Get user input for country code and version number
    country_code = input("Enter country code XXX (default 124): ")
    country_code = country_code if country_code else "124"

    version = input("Enter version number XXX (default 101): ")
    version = version if version else "101"

    # Generate and print serial numbers
    serial = generate_agi_serial(country_code, version)
    print("Serial Number: ", serial)
    print(len(serial))

    # Generate UUID from the serial number
    uuid_result = serial_to_uuid(serial)
    print("Secret Key: ", uuid_result)
    print(len(uuid_result))

if __name__ == "__main__":
    main()
