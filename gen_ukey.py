# gen_ukey.py
# Generate user base key (UUID) for Amazing GRACE infrastructure

# Import necessary modules and configuration
from datetime import datetime
import hashlib
import random
import string
import uuid

USER_NAME = "Jon_Doe"
USER_EMAIL = "<EMAIL>"
USER_JOIN = datetime.strptime("2025-01-01 00:00:00", "%Y-%m-%d %H:%M:%S")  # User join date and time

# Define the year and month codes for the serial number
YEAR_CODE_MAP = {
    2020: 'W',
    2021: 'X',
    2022: 'A',
    2023: 'S',
    2024: 'B',
    2025: 'P',
    2026: 'G',
    2027: 'L',
    2028: 'M',
    2029: 'T',
    2030: 'F',
    2031: 'Q',
    2032: 'E',
    2033: 'J',
    2034: 'D',
    2035: 'O',
    2036: 'N',
    2037: 'I',
    2038: 'Y',
    2039: 'H',
    2040: 'U',
    2041: 'V',
    2042: 'R',
    2043: 'K',
    2044: 'Z',
    2045: 'C'
}

# Define the month codes for the serial number
MONTH_CODE_MAP = {
    1: "J",   # January
    2: "F",   # February
    3: "M",   # March
    4: "A",   # April
    5: "Y",   # May
    6: "U",   # June
    7: "L",   # July
    8: "G",   # August
    9: "S",   # September
    10: "O",  # October
    11: "N",  # November
    12: "D",  # December
}

def generate_user_id(user_name : str = USER_NAME, user_email : str = USER_EMAIL) -> str:
    """
    Generate a user ID for the Amazing GRACE infrastructure.

    Args:
        user_name (str, optional): The name of the user. Defaults to "Jon_Doe".
        user_email (str, optional): The email of the user. Defaults to "<EMAIL>".

    Returns:
        str: The generated user ID.
    """
    # Generate year, month, day codes
    year_code = YEAR_CODE_MAP.get(USER_JOIN.year)
    month_code = MONTH_CODE_MAP.get(USER_JOIN.month)
    day_code = format(USER_JOIN.day, "02X")

    # Combine the name and email and hash them to generate a unique user ID (first 12 characters)
    rand_str = ''.join(random.choices(string.ascii_letters + string.digits, k=8))
    to_hash = user_name.lower() + user_email.lower() + rand_str
    sha = hashlib.sha256(to_hash.encode()).hexdigest()
    unifier = sha[:12].upper()

    # Combine the base user ID with the unifier (all uppercase) and return it as the user ID (first 12 characters)
    base_user_id = f"AGi{year_code}{month_code}{day_code}{unifier}".upper()
    return base_user_id

def userid_to_uuid(user_id : str) -> str:
    """
    Convert a user ID to a UUID.

    Args:
        user_id (str): The user ID to convert.

    Returns:
        str: The generated UUID.
    """
    # Convert the user ID to bytes
    user_id_bytes = user_id.encode('utf-8')
    
    # Use the first 16 bytes of the hash to generate the UUID (UUID version 5)
    generated_uuid = uuid.uuid5(uuid.NAMESPACE_DNS, user_id_bytes)  # UUID version 5 with SHA-1 hash algorithm (Namespace: DNS)

    # Convert the UUID to a upper case string and add the AGi- prefix
    generated_uuid = f"AGi-{str(generated_uuid).upper()}"
    
    return generated_uuid
def main():
    """
    Main function to generate the user ID.
    """
    
    # Generate and print user ID
    user_id = generate_user_id()  # Generate user ID with default name and email
    while "34" not in user_id:
        user_id = generate_user_id()  # Generate user ID with default name and email
    print("User ID: ", user_id)  # Print the generated user ID
    print(len(user_id))

    # Generate UUID from the user ID
    uuid_result = userid_to_uuid(user_id)  
    print("User-Base Key: ", uuid_result)
    print(len(uuid_result))

if __name__ == "__main__":
    main()
