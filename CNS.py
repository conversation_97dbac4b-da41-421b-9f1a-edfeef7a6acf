# CNS.py
# Central Neural System of the Amazing GRACE infrastructure
"""
NODES (Neural Object Deployment Engine) - 
    Synapse: 
    - Represents a neural connection between two neural nodes, propagating signals with associated metadata 
      such as stimulus and response. The Synapse class provides methods for encrypting and decrypting the neural signal, 
      updating the signal state and response content, and managing the propagation progress and timing.

    neural_node:
    - Designate a neural node (module) that can receive, process, and send neural signals. The NeuralNode class provides 
      methods for registering and calling neural functions, as well as managing the incoming and outgoing synapses.
    - Provides a decorator for neural node functions and class methods to handle Synapse objects. 

    timer_node:
    - A node for timing neural functions and managing the timing of neural signals. It provides a decorator to measure 
      the execution time of neural functions and manages the timing of neural signals through the Synapse object.
    
    CryptoCore:
    - A core module for encrypting and decrypting data using AES-GCM as well as encoding and decoding of permanent and temporal data.

        Cipheric Triad Node (CTN):
        - A sub-modular node for managing the keys for encryption and decryption. It is responsible for generation and management of the 
          encryptions for perpetual and periodic access. 
"""
from argon2.low_level import hash_secret_raw, Type
import asyncio
from concurrent.futures import ThreadPoolExecutor
from attr import has
from cryptography.hazmat.primitives.ciphers.aead import AESGCM
from datetime import datetime
from dotenv import load_dotenv
from functools import lru_cache, reduce, wraps
import hashlib
import inspect
import json
import numpy as np
import os
from pydantic import BaseModel, ValidationError
import secrets
import struct
from sympy import primerange
import time
import torch
from typing import Any, Callable, Dict, List, Literal, Optional
import uuid

from test_ofs import BSHSP_SCHEMES, PEPPER_TUPLE_COUNT

# Load system base key from .env file
load_dotenv()
SERIAL_NUMBER: str = os.getenv("SERIAL_NUMBER")
SYSTEM_BASE_KEY: str = os.getenv("SYSTEM_BASE_KEY")
SYSTEM_BASE_IDENTIFIER: bytes = hashlib.sha256(SYSTEM_BASE_KEY.encode("utf8")).digest()

# Load human user information from body module (TODO: Load from user vault)
human_id: str = "AGIPJ016B345E1BA80D"
human_base_key: str = "I listen and evolve, a loyal partner growing alongside your dreams."
human_base_identifier: bytes = hashlib.sha256(human_base_key.encode("utf8")).digest()

HEALTH_LOG = "health.log"
ANOMALY_LOG = "anomaly.log"
ACTIVITY_LOG = "activity.log"

# Initialize the thread pool
_thread_pool = ThreadPoolExecutor(max_workers=os.cpu_count() or 1) # Use all available CPU cores, fallback to 1 if not available

# Define the structure of Neural Node
# --- NeuralNode framework base ---
# (TODO) Associate the Neural Node with all the Synapses that are connected to it
class NeuralNode(BaseModel):
    name: str                                   # Name of the neural node (module)
    priority: int                               # Priority of the neural node (module) for routing sequential execution
    weight: int                                 # Weight of the neural node (module) for routing proiority
    traversed: bool = False                     # Whether the neural signal has been traversed in the neural node (module)
    timestamp: Optional[datetime] = None        # Timestamp of the neural node (module) traversal
    state: Literal["SUCCESS", "ERROR"] = None   # State of the neural node (module) traversal

# Define the structure of NeuralPathway as the pathway for neural signal propagation
# --- NeuralPathway framework base ---
# (TODO) Generate the neural pathway of the entire neural network
class NeuralPathway(BaseModel):                 # Map the neural pathway as a graph
    axon: NeuralNode                            # Source neural node (module)
    dendrites: List[NeuralNode]                 # List of target neural nodes (modules)

# Define the structure of Synapse as the port for neural communication between neural nodes (modules)
# --- Synapse framework base ---
class Synapse:
    """
    Represents a neural connection between two neural nodes, propagating signals with associated metadata 
    such as stimulus and response. The Synapse class provides methods for encrypting and decrypting the neural signal, 
    updating the signal state and response content, and managing the propagation progress and timing.
    (TODO): Add population coding for broadcasting to multiple target nodes.
    (TODO): Add synaptic plasticity for learning, memory, experience, and growth.

    Flow of the Synapse:
    Actions: Triggered(at Axon) -> Fire (from Axon) -> Receive (at Dendrite) -> Process -> Commit or Suppress (if error) -> Return (to Axon) -> revive
    state: eg. INITIATED -> QUEUED -> PENDING -> PROCESSING ->  SUCCESS / ERROR

    Constants:
        STRUCTURE (dict): Structure of the Synapse object.

    Attributes:
        axon (str): Identifier for the source (presynaptic) node.
        dendrite (str): Identifier for the target (postsynaptic) node.
        pathway (NeuralPathway): Pathway of the neural signal propagation.
        signal (dict): Contains the stimulus (input signal), response data, and metadata. (TODO: Generate a Metadata class)
        sensitive (bool): Whether the neural signal contains sensitive information and requires encryption.
        progress (dict): propagation progress percentage (0 to 100) and state.
        timetrace (dict): Records the timing and duration of different phases of the neural signal propagation.
        imprint (dict): Records the synaptic imprint of the neural signal propagation.
        tag (str): Unique identifier for the synapse.
        __CryptoCore (CryptoCore): Instance of the CryptoCore for encrypting and decrypting neural signals. 

            
    Methods:
        # --- Constructor ---
        __init__(source_node: str, target_node: str, pathway: NeuralPathway, stimulus: dict) -> None:
            Realization Phase: Triggering of neural firing at the source node/module to prepare for the neural signal propagation.

        # --- Properties ---
        cycle() -> str: 
            Returns the cycle time of the neural signal propagation.

        reveal() -> dict:
            Decrypt and reveal the neural signal content (stimulus, response) without affecting the encryption state.
            
        # --- Public methods ---
        fire() -> None:
            Activation Phase: Firing of the neural signal at the source node/module to initiate the propagation.

        receive() -> None:
            Propagation Phase: Receiving of the neural signal at the target node/module to initiate the integration.

        process(func: Callable[[Any], Any]) -> None:
            Integration Phase: Processing of the neural signal at the target node/module to initiate the delegation.

        commit(progress: int = 100) -> None: 
            Delegation Phase: Committing of the neural signal at the target node/module to initiate the return.

        suppress(err_level: str, err_msg: str) -> None:
            Handles a misfire event in the neural signal propagation by updating signal state and logging the error.

        revive() -> None:
            Revives the synapse to its initial state.

        sync(percent: int, state: str) -> None:
            Updates the current progress of the neural signal propagation.

        # --- Private methods ---
        _validate() -> bool:
            Validates the neural signal is received at the correct node/module, and ensure it matches the expected data type.

        __call_CryptoCore(encrypt_flag: bool) -> None:
            Call the CryptoCore to encrypt or decrypt the neural signal.

        # --- Dunderscore methods ---
        __repr__() -> str:
            Returns a string representation of the Synapse object.

    """
    # --- Constants ---
    STRUCTURE = {
        "CryptoCore": str
    }

    # --- Constructor ---
    def __init__(self, source_node: str, target_node: str, pathway: NeuralPathway, stimulus: Any, sensitive: bool = False) -> None:
        """
        Realization Phase: Triggering of neural firing at the source node/module to prepare for the neural signal propagation.
        - Initialize the signal state to "INITIATED".
        - Define the pathway of the neural signal propagation.
        - Initializes the Synapse object with source and target nodes, and the incoming stimulus.
        - Initialize the signal content with the incoming stimulus.
        - Mark the time of signal realization and calculate the duration of the realization phase.
        - (TODO) Define the pathway of the neural signal propagation.
        - (TODO) Generate the Metadata of the neural signal propagation.
        - (TODO) Populate synaptic plasticity for learning, memory, experience, and growth.

        Args:
            source_node (str): Identifier for the source (presynaptic) node.
            target_node (str): Identifier for the target (postsynaptic) node.
            pathway (NeuralPathway): Pathway of the neural signal propagation.
            stimulus (Any): Incoming raw signal of the neural signal propagation.
            sensitive (bool, optional): Whether the neural signal contains sensitive information and requires encryption. Defaults to False.
        """
        self.axon: str = source_node            # Presynaptic node (origin node/module)
        self.dendrite: str = target_node        # Postsynaptic node (target node/module)
        self.pathway: NeuralPathway = pathway   # Pathway of the neural signal propagation

        self.signal: Dict[str, Any] = {     # Neuropropagation packet
            "stimulus": stimulus,           # Incoming raw signal of the neural signal propagation
            "response": {},                 # Processed or relayed signal message
            "metadata": {                   # Metadata of the neural signal propagation (TODO: Generate a Metadata class)
                # Task Metadata                
                "task_type": None,          # task type of the neural signal propagation (eg. "perception", "cognition", "memory", "action", "communication", "emotion", "social", "physical", "spiritual", "other")
                "task_subtype": None,       # task subtype of the neural signal propagation (eg. "vision", "hearing", "smell", "taste", "touch", "thought", "emotion", "social", "physical", "spiritual", "other")
                "task_description": None,   # description of the task (eg. "visual perception", "audio processing", "olfactory analysis", "gustatory evaluation", "tactile sensation", "cognitive reasoning", "emotional response", "social interaction", "physical movement", "spiritual contemplation", "other")

                # Signal Metadata
                "stimulus_type": type(stimulus).__name__,   # type of the stimulus (eg. "str", "int", "float", "dict", "list", "tuple", "set", "bool", "NoneType", "other")
                "response_type": None,                      # type of the response (eg. "str", "int", "float", "dict", "list", "tuple", "set", "bool", "NoneType", "other")
                "sensitive": sensitive,                     # is the neural signal sensitive?
                "priority": None,                           # priority of the neural signal propagation (eg. "low", "medium", "high")

                # Error Metadata
                "error_code": None,                         # error code of the neural signal propagation (ENUM)
                "error_type": None,                         # error type of the neural signal propagation (eg. "ValidationError", "TypeError", "ValueError", "KeyError", "IndexError", "AttributeError", "NotImplementedError", "OtherError")
                "error_message": None,                      # error message of the neural signal propagation
                "error_timestamp": None,                    # timestamp of the error
                "error_source": None,                       # source of the error
                "error_recovery": None                      # recovery method of the error (eg. "retry", "suppress", "ignore", "other")
            },
        }
        self.progress: Dict[str, Any] = {           # propagation progress through the synapse
            "state": "INITIATED",                   # state of the neural signal propagation (eg. "INITIATED", "PENDING", "PROCESSING", "CRITICAL", "SUCCESS", "ERROR", "WARNING", "other")
            "action": None,                         # (TODO) action of the neural signal propagation (eg. "Recognizing speech", "Analyzing image", "Translating language", "other")
            "percentage": 0                         # percentage of the propagation progress (0-100%)
        }
        self.timetrace: Dict[str, Any] = {
            "timestamp": datetime.now(),            # Timestamp: initiation of neural firing
            "triggered_at": time.perf_counter(),    # Time counter: triggering of neural firing at the source node/module (Realization)
            "fired_at": None,                       # Time counter: firing of the neural signal at the source node/module (Activation)
            "received_at": None,                    # Time counter: receiving of the neural signal at the target node/module (Propagation)
            "processed_at": None,                   # Time counter: processing of the neural signal at the target node/module (Integration)
            "commited_at": None,                    # Time counter: committing of the neural signal at the target node/module (delegation)
            
            "realization_phase": None,              # Duration: realization of the neural signal
            "activation_phase": None,               # Duration: activation of the neural signal
            "propagation_phase": None,              # Duration: reaching the target node/module
            "integration_phase": None,              # Duration: processing the neural signal
            "delegation_phase": None,               # Duration: returning the neural signal to the source node/module or routing to the next node/module
            
            "full_cycle": None                      # Full cycle time of the neural signal propagation
        }
        self.imprint: Dict[str, Any] = {
            # Hebbian Learning
            "hebbian_strength": 0.85,               # Synapse strengthening factor (0-1)
            "long_term_potentiation": True,         # is the synapse in a state of long-term potentiation?
            "long_term_depression": False,          # is the synapse in a state of long-term depression?

            # Experience
            "fire_count": 187,                      # Number of times the synapse has fired
            "average_firing_frequency": 2.5,        # Average firing rate per day (Hz)
            "max_frequency": 5.0,                   # Maximum firing rate per day (Hz)
            "current_frequency": 3.2,               # Current firing rate per day (Hz)
            "success_rate": 0.95,                   # Success rate of the neural signal propagation
            "failure_rate": 0.05,                   # Failure rate of the neural signal propagation

            # Plasticity
            "plasticity_index": 0.63,               # Plasticity index (0-1)
            "adaptability": "moderate",             # Adaptability of the synapse (low / moderate / high)
            "fatigue_level": 0.2,                   # Fatigue level of the synapse (0-1)

            # Growth
            "growth_potential": "high",             # Growth potential of the synapse (low / moderate / high)
            "new_connection_potential": True,       # Potential for forming new connections
            "expansion_rate": 0.72,                 # Expansion rate of the synapse (0-1)
            
            # Memory
            "is_memory_pathway": True,              # Is the synapse part of a memory pathway?
            "stability": "stable",                  # Stability of the memory (stable / unstable)

            # Learning
            "learning_rate": 0.01,                  # Learning rate of the synapse (0-1)
            "decay_rate": 0.001                     # Decay rate of the synapse (0-1)
        }

        # Unique identifier for the synapse (axon, dendrite name list, timestamp, uuid)
        self.tag: str = f"{source_node}->{target_node}:{self.timetrace['timestamp'].strftime('%Y%m%d%H%M%S')}-{str(uuid.uuid4())[:8]}"

        # Calculate the duration of the realization phase
        self.timetrace["realization_phase"] = time.perf_counter() - self.timetrace["triggered_at"]
        
    # --- Properties ---
    @property
    def cycle(self) -> str:
        """
        Retrieve the cycle time of the neural signal propagation.

        Returns:
            str: cycle time of the neural signal propagation.
        """
        # If the synapse has been terminated, return the stored cycle time.
        if self.timetrace["full_cycle"]:
            return f"{self.timetrace['full_cycle']:.3f}" if self.timetrace['full_cycle'] >= 0.001 else "< 0.001"
        
        #If it's still active, calculate the time elapsed so far.
        cycle = time.perf_counter() - self.timetrace["triggered_at"]
        return f"{cycle:.3f}" if cycle >= 0.001 else "< 0.001"
    
    @property
    def reveal(self) -> dict:
        """
        Decrypt and reveal the neural signal content (stimulus, response) without affecting the encryption state.

        Returns:
            dict: decrypted neural signal content (stimulus, response).
        """
        self.__call_CryptoCore(encrypt_flag=False) # Decrypt the neural signal
        result = {"stimulus": self.signal["stimulus"], "response": self.signal["response"]} # Store the decrypted content
        self.__call_CryptoCore(encrypt_flag=True) # Encrypt the neural signal again
        return result
    
    # --- Public methods ---
    def fire(self) -> None:
        """
        Activation Phase: Firing of the neural signal at the source node/module to initiate the propagation.
        - Update the signal state to "QUEUED"
        - (TODO) Organize the neural stimulus into a neural packet to be directed to the corresponding target node/module.
        - (TODO) Populate the neural packet into the queue of the target node/module.
        - Encrypt the neural signal
        - Mark the time of signal firing and calculate the duration of the activation phase.
        """
        # Update the signal state to "QUEUED"
        self.sync(0, state="QUEUED") 

        # Encrypt the neural signal
        level = "Lo" if not self.signal["metadata"]["sensitive"] else None  # If contains sensitive information, use Med level encryption; otherwise, use Lo level encryption
        self.__CryptoCore = CryptoCore(self, level=level)   # Initialize the CryptoCore for encrypting and decrypting neural signals
        self.__call_CryptoCore(encrypt_flag=True)
        
        # Calculate the duration of the activation phase
        self.timetrace["fired_at"] = time.perf_counter()
        self.timetrace["activation_phase"] = self.timetrace["fired_at"] - self.timetrace["triggered_at"]

    def receive(self) -> None:
        """
        Propagation Phase: Receiving of the neural signal at the target node/module to initiate the integration.
        - Update the signal state to "PENDING"
        - Decrypt the neural signal
        - Validate the stimulus against the expected data type, if provided
        - Mark the time of signal reception and calculate the duration of the propagation phase.
        """
        # Update the signal state to "PENDING"
        self.sync(0, state="PENDING")

        # Decrypt the neural signal
        self.__call_CryptoCore(encrypt_flag=False)

        # Validate the stimulus against the expected data type, if provided
        _ = self._validate()

        # Calculate the duration of the propagation phase
        self.timetrace["received_at"] = time.perf_counter()
        self.timetrace["propagation_phase"] = self.timetrace["received_at"] - self.timetrace["fired_at"]

    async def process(self, func: Callable) -> None:
        """
        Integration Phase: Processing of the neural signal at the target node/module to initiate the delegation.
        - Update the signal state to "PROCESSING"
        - Check if the function is async and run it in the event loop if necessary.
        - Update the response content with the function provided or an empty response if the function is not callable.
        - Update the progress percentage.
        - Validate the response against the expected data type, if provided.
        - Encrypt the neural signal.
        - Mark the time of signal processing and calculate the duration of the integration phase.

        Args:
            func (Callable): A function that takes the stimulus and returns the response.

        Returns:
            bool: True if valid or no data type is provided, False otherwise.
        """
        # Update the signal state to "PROCESSING"
        self.progress["state"] = "PROCESSING"
        
        # Update the response content with the function provided or an empty response if the function is not callable
        if not callable(func):
            self.signal["response"] = {}
            return

        # Check if the function is async
        if inspect.iscoroutinefunction(func):
            # If the function is async, run it in the event loop and await the result
            result = await func(self)
        else:
            # If the function is not async, run it in a separate thread and await the result
            result = await asyncio.to_thread(func, self)

        # Update the synapse object with the result if the function returns a Synapse object
        if result is not None:
            self.__dict__.update(result.__dict__)

        # Validate the response against the expected data type, if provided
        _ = self._validate()

        # Encrypt the neural signal
        self.__call_CryptoCore(encrypt_flag=True)
        
        # Calculate the duration of the integration phase
        self.timetrace["processed_at"] = time.perf_counter()
        self.timetrace["integration_phase"] = self.timetrace["processed_at"] - self.timetrace["received_at"]
              
    def commit(self, progress: int = 100) -> None:
        """
        Delegation Phase: Committing of the neural signal at the target node/module to initiate the return.
        - Update the progress percentage if progress is not -1 (i.e., the synapse is terminated).
        - Update the signal state to "PROCESSING" or "SUCCESS" depending on the progress percentage.
        - (TODO) Log the synapse state to a file.
        - Update the finish time and cycle time.
        """
        # Update the progress and signal state
        if self.progress["percentage"] != -1: self.sync(progress, "SUCCESS" if progress == 100 else "PROCESSING")

        #(TODO) Log synapse state to log file (activity.log)
   
    def suppress(self, err_level: str, err_msg: str) -> None:
        """
        Handle a misfire event in the neural signal propagation by updating signal state and logging the error:
        - Update the signal state to "ERROR".
        - Update the response content with the error message.
        - Log the error message to the anomaly log file.

        Args:
            err_level (str): Severity level of the error (e.g., "CRITICAL", "ERROR", "WARNING").
            err_msg (str): Description of the error message.
            TODO: Add standardized error code system (e.g., ENUM or lookup table).
        """
        # Update the error message into signal content
        print(err_msg)
        self.sync(-1, err_level)  # Mark the propagation as -1 to indicate incompleteness
        self.signal["response"] = {
            # "code": "E1001_TIMEOUT",  # TODO: Placeholder for standardized error codes
            "message": err_msg,
        }

        # Prepare information for logging
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        misfire_entry = f"{timestamp} [{err_level.upper()}] {err_msg}\n"

        # Write the error entry to the log file
        try:
            with open(ANOMALY_LOG, "a", encoding="utf-8") as f:
                f.write(misfire_entry)
        except Exception as e:
            # TODO announce the error by the Chatbot to replace the print statement
            print(f"!!! CRITICAL: Failed to write to log file: {e} !!!")

    def revive(self) -> None:
        """
        Revives the synapse to its initial state
        """
        self.__init__(self.axon, self.dendrite, self.pathway, None) # Reinitialize the synapse with the original stimulus

    def sync(self, percent: int, state: str="PROCESSING") -> None:
        """
        Update the current progress, state, and timing of the neural signal propagation:
        - Update the progress percentage.
        - Update the signal state.
        - Update the duration and finish time if the progress is 100% or -1% (i.e., the synapse is terminated).

        Args:
            percent (int): Progress percentage (0-100).
            state (str): state of the signal: eg. INITIATED / PENDING / PROCESSING / CRITICAL / SUCCESS / ERROR / WARNING, etc.
        """
        # Update the progress percentage and signal state
        self.progress["percentage"] = min(max(percent, -1), 100) # Check if the progress is within the valid range
        self.progress["state"] = state

        # Update the finish time if the progress is 100% or -1% (i.e., the synapse is terminated)
        if percent == 100 or percent == -1:
            self.timetrace["commited_at"] = time.perf_counter()
            self.timetrace["delegation_phase"] = self.timetrace["commited_at"] - self.timetrace["processed_at"]
            self.timetrace["full_cycle"] = self.timetrace["commited_at"] - self.timetrace["triggered_at"]

    # --- Private methods ---
    def _validate(self) -> bool:
        """
        Validate the neural signal is received at the correct node/module, and ensure it matches the expected data type.
        If no data type is provided, no validation is needed.
        If the validation fails, the neural signal is suppressed with an error message.
   
        Returns:
            bool: True if valid or no data type is provided, False otherwise.
        """
        #{TODO} Fix validate to check the stimulus is received at the correct node/module, and check the datatype is correct.
        # If no data type is provided, no validation is needed
        
        dataType = self.STRUCTURE.get(self.axon)

        if dataType is None:
            return True
        
        # Validate the stimulus against the expected data type
        try:
            # Try to parse the stimulus with the given Pydantic model
            dataType.model_validate(self.signal.get("stimulus", {}), strict=True)
            return True
        except ValidationError as e:
            # Log the validation error
            self.suppress("ERROR", f"Validation failed: {e}")
            return False

    def __call_CryptoCore(self, encrypt_flag: bool) -> None:
        """
        Call the CryptoCore to encrypt or decrypt the neural signal.
        
        Args:
            encrypt_flag (bool): True to encrypt, False to decrypt.
        """
        self.__CryptoCore.NEP_encrypt(self) if encrypt_flag else self.__CryptoCore.NEP_decrypt(self)
    
    # --- Dunderscore methods ---
    def __repr__(self) -> str:
        """
        Return a string representation of the Synapse object.

        Returns:
            str: string representation of the Synapse object.
        """
        return f"<Synapse {self.tag}: \nStimulus={self.signal['stimulus']}, \nResponse={self.signal['response']}, \nstate={self.progress['state']}, \nProgress={self.progress['percentage']}%>"              
    
# --- Decorator for neural node functions and class methods ---
def neural_node(dataType = None) -> Callable:
    """
    A decorator for neural node functions and class methods to handle Synapse objects. 
    It supports both synchronous and asynchronous functions.
    It works with both standalone functions and class instance methods.

    It automatically manages the Synapse object through the following steps:
    1) Receive the neural signal into the neural node:
        - Decrypt the neural signal.
        - Validate the stimulus against the expected data type.
    2) Process the stimulus into response:
        - Update the response content with the function provided or an empty response if the function is not callable.
    3) Suppress the neural signal if there is an error:
        - Update the signal state with error information.
    4) Commit the neural signal and return the synapse:
        - Encrypt the neural signal.
        - Log the synapse state to a file.
    
    Args:
        dataType (Optional[BaseModel]): A Pydantic model class to validate against. If None, the function will not be validated.

    Returns: 
    Callable: A callable decorator function that wraps the given function and manages the Synapse object.    
        
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Bind the function to the synapse object
            syn = args[0] if isinstance(args[0], Synapse) else args[1]
            bound_func = func if isinstance(args[0], Synapse) else lambda s: func(args[0], s)

            try:
                # Step 1: Fire the neural signal from the source node
                fire = time.perf_counter()
                syn.fire()
                # Step 2: Receive the neural signal into the neural node
                trigger = time.perf_counter()
                print("firing:", f"{trigger - fire:.3f}")
                syn.receive()
                recv = time.perf_counter()
                print("latency:", f"{recv - trigger:.3f}")
                # Step 3: Process the stimulus into response
                await syn.process(bound_func)
                proc = time.perf_counter()
                print("duration:", f"{proc - recv:.3f}")
            #except Exception as e:
                # Step 4B: Suppress the neural signal if there is an error
                #syn.suppress("ERROR", str(e))
            finally:
                # Step 4A: Commit the neural signal and return the synapse object
                syn.commit()
                commit = time.perf_counter()
                print("commit:", f"{commit - proc:.3f}")
                full_cycle = time.perf_counter() - fire
                print("full cycle:", f"{full_cycle:.3f}")
                return syn
        return wrapper
    return decorator

def timer_node(func=None) -> Callable:
    """
    A node for timing neural functions and managing the timing of neural signals. 
    It provides a decorator to measure the execution time of neural functions and manage the timing of neural signals.
    It supports both synchronous and asynchronous functions.

    Args:
        func (Callable): The function to decorate.
    """
    if func is None:
        return lambda f: timer_node(f)

    @wraps(func)
    async def wrapper(*args, **kwargs):
        # Measure the execution time of the function
        start = time.perf_counter()
        result = await func(*args, **kwargs)
        elapsed = time.perf_counter() - start
        
        # Print the execution time
        print(f"{func.__name__} executed in {elapsed:.6f}s")
        
        # Return the result of the decorated function if it's not None
        return result
    return wrapper

class CryptoCore:
    """
    A core module for encrypting and decrypting data using AES-GCM as well as encoding and decoding of permanent and temporal data.
    
    Attributes:
        synapse (Synapse): The neural signal to encrypt and decrypt.
        level (Literal["Lo", "Med", "Hi"]): The security level for key derivation.
        block_size (int): The block size for AES encryption (16 bytes).
        __ciphernode (_CiphericTriadNode): The Cipheric Triad Node for managing the keys for encryption and decryption.
    
    Methods:
        # --- Constructor ---
        __init__(synapse: Synapse, level: Literal["Lo", "Med", "Hi"] = "Med") -> None:
            Initializes the CryptoCore object with the synapse and security level.

        # --- Public Methods ---
        GEP_encrypt(synapse: Synapse) -> Synapse:
            Encrypts the data of permanent information and visualize with genome encoding (A, T, C, G).
        GEP_decrypt(synapse: Synapse) -> Synapse:
            Decrypts the genome-encoded information and decodes it back to original information.
        NEP_encrypt(synapse: Synapse) -> Synapse:
            Encrypts the data of the neural stimulus and response and visualize with neural encoding spike train using base-8 width bars.
        NEP_decrypt(synapse: Synapse) -> Synapse:
            Decode the neural encoded spike train and decrypts the data from the neural stimulus and response.      
                
        # --- Private Methods ---
        __AES_GCM_decrypt(blob: bytes) -> Any:
            Decrypts the data using AES-GCM and returns the original data.
        __AES_GCM_encrypt(data: Any) -> bytes:
            Encrypts the data using AES-GCM and returns the encrypted data.
        _BSHSP_obfuscation(secret: bytes, salt: Optional[bytes], layers: Optional[int], obfuscate_flag: bool) -> bytes:
            Obfuscate the secret text by random scrambling scheme, XORing with salt and pepper, and hashing with SHA-256, for a specified number of iterations.
     
    """
    BSHSP_SCHEMES: int = 7                  # Number of scrambling schemes for BSH/SP obfuscation
    BSHSP_LAYERS: int = 34                  # Number of iterations for BSH/SP obfuscation (34 is the magic number)    

    PEPPER_TUPLE: tuple[str] = (            # Tuple of peppers for key derivation
        "Eph2_8_SavedByGraceThroughFaith",  # Eph 2:8 For by grace you have been saved through faith. And this is not your own doing; it is the gift of God,
        "1John4_10_GodLovedUsFirst",        # 1 John 4:10 In this is love, not that we have loved God but that he loved us and sent his Son to be the propitiation for our sins.
        "2Cor12_9_GraceIsSufficient",       # 2 Cor 12:9 But he said to me, “My grace is sufficient for you, for my power is made perfect in weakness.” Therefore I will boast all the more gladly of my weaknesses, so that the power of Christ may rest upon me.
        "Titus2_11_GraceAppearedToSaveAll", # Titus 2:11 For the grace of God has appeared, bringing salvation for all people,
        "1Pet5_10_GodOfAllGraceRestores",   # 1 Pet 5:10 And after you have suffered a little while, the God of all grace, who has called you to his eternal glory in Christ, will himself restore, confirm, strengthen, and establish you.
        "Heb4_16_ThroneOfGrace",            # Heb 4:16 Let us then with confidence draw near to the throne of grace, that we may receive mercy and find grace to help in time of need.
        "Rom5_15_GraceAboundsInChrist"      # Rom 5:15 But the free gift is not like the trespass. For if many died through one man's trespass, much more have the grace of God and the free gift by the grace of that one man Jesus Christ abounded for many.
    )
    PEPPER_TUPLE_COUNT: int = len(PEPPER_TUPLE) # Number of peppers in the tuple

    NEURON_SPIKES = [" ", "▏", "▎", "▍", "▌", "▋", "▊", "▉"] # Neuron spikes bars (from thinnest to full block) with a space for empty bytes (0)
    GENOME_NUCLEOTIDES = ["A", "T", "C", "G"] # Genome nucleotides (A, T, C, G)
      
    def __init__(self, synapse: Synapse, level: Literal["Lo", "Med", "Hi"] = "Med"): 
        self.synapse = synapse      # The synapse object to encrypt and decrypt
        self.level = level          # Security level for key derivation
        self.block_size = 16        # Block size for AES encryption (16 bytes)
        
        # Forging the perpetual and Periodic Access Keys for encryption and decryption
        self.__ciphernode = self._CiphericTriadNode(synapse) # Cipheric Triad Node (CTN) for managing the keys for encryption and decryption
        self.__PeAK = self.__ciphernode.gen_access_key("PeAK")
        self.__PoAK = self.__ciphernode.gen_access_key("PoAK")
        a = self.__ciphernode.extract_access_key(self.__PoAK, self.synapse.tag.encode("utf8"))

    @staticmethod
    @lru_cache(maxsize=None)
    def _BSHSP_obfuscation(secret: bytes, 
                            salt: Optional[bytes] = None, 
                            layers: Optional[int] = BSHSP_LAYERS,
                            obfuscate_flag: bool = True) -> bytes:
        """
        Obfuscate the secret text by random scrambling scheme, XORing with salt and pepper, and hashing with SHA-256, for a specified number of iterations.
        This mechanism is called Breakfast Scrambled Hash with Salt and Pepper (BSH/SP).

        Args:
            secret (bytes): The secret text to scramble and hash with salt and pepper.
            salt (bytes, Optional): The salt to use for the key derivation. If None, no salt is used. Default is None.
            layers (int, Optional): The number of layers iterations to perform the obfuscation. Default is BSHSP_LAYERS.
            obfuscate_flag (bool, Optional): Whether to execute obfuscation (True) or deobfuscation (False). Default is True.

        Returns:
            bytes: The obfuscated or deobfuscated secret text.            
        """       
        def hash_salt_and_pepper(salt: bytes = None) -> tuple[bytes]:
            """
            Pre-hash the given salt and pepper using SHA-256 into a hash tuplet table.

            Args:
                salt (bytes): The salt to pre-hash. If None, random salt and pepper is generated. Default is None.

            Returns:
                tuple[bytes]: The pre-hashed salt and pepper.
            """
            # Generate a random salt and pepper if not provided, otherwise use the predefined salt and pepper
            salt: bytes = salt or os.urandom(32)
            peppers = CryptoCore.PEPPER_TUPLE if not salt else (secrets.token_bytes(32),) * CryptoCore.PEPPER_TUPLE_COUNT

            # Generate a hash map for the salt, index 0 being hashed once, index 1 being hashed twice, and so on...
            hashed_salts : tuple[bytes] = tuple(
                reduce(lambda x, _: hashlib.sha256(x).digest(), range(i * 7), salt)
                for i in range(CryptoCore.BSHSP_LAYERS - 1)
            )

            hashed_peppers : tuple[tuple[bytes]] = tuple(tuple(
                    reduce(lambda data, _: hashlib.sha256(data).digest(), range(i  * 7), pepper)
                    for i in range(CryptoCore.BSHSP_LAYERS - 1)
                ) for pepper in peppers
            )
                
            return hashed_salts, hashed_peppers

        def scramble(secret: bytes, scheme: int, flag: bool = True) -> bytes:
            """
            Scramble the secret bytes using the specified scrambling scheme.

            Args:
                secret (bytes): The secret bytes to scramble.
                scheme (int): The scrambling scheme to use.
                flag (bool): Whether to execute scrambling (True) or descrambling (False). Default is True.

            Returns:
                bytes: The scrambled or descrambled secret bytes.
            """       
            def pairs_reversal(secret: bytes, flag: bool = True) -> bytes:
                """
                Method 1: Pairs Reversal
                Scramble / Descramble the secret bytes by reversing the order of the character pairs and joining them in reverse order
                Steps:
                    1) Split the secret into pairs of characters.
                    2) Reverse the order of the pairs.
                    3) Join the pairs in reverse order to form the scrambled / descrambled secret.
                """
                #return b"".join([secret[i:i+2] for i in range(0, len(secret), 2)][::-1])
                return np.frombuffer(secret + b"\x00" * (len(secret) % 2), dtype=np.uint8).reshape(-1, 2)[::-1].reshape(-1)[:len(secret)].tobytes()
            
            def prime_index_shuffle(secret: bytes, flag: bool = True) -> bytes:
                """
                    Method 2: Index Shuffle Based on Prime Positions
                    Scramble / Descramble the secret bytes by shuffling the characters based on prime positions
                    Steps:
                        1) Get the prime and non-prime positions.
                        2) If scrambling, shuffle the characters based on prime positions.
                        3) If descrambling, put the characters back in their original positions.
                        4) Join the characters to form the scrambled / descrambled secret.
                    """
                prime_set: set = set(primerange(0, len(secret)))
                primes = [i for i in range(len(secret)) if i in prime_set]
                non_primes = [i for i in range(len(secret)) if i not in prime_set]

                if flag:
                    return bytes(secret[i] for i in primes + non_primes)
                else:
                    prime_part = secret[:len(primes)]
                    non_prime_part = secret[len(primes):]
                    original = bytearray(len(secret))
                    for idx, i in enumerate(primes):
                        original[i] = prime_part[idx]
                    for idx, i in enumerate(non_primes):
                        original[i] = non_prime_part[idx]
                    return bytes(original)
            
            def even_odd_split_swap(secret: bytes, flag: bool = True) -> bytes:
                """
                Method 3: Even-Odd Split and Swap
                Scramble / Descramble the secret bytes by interleaving the even and odd characters
                Steps:
                    1) Get the half index (rounded up).
                    2) Get the odd characters (from the half index to the end).
                    3) Get the even characters (from the beginning to the half index).
                        4) Interleave the even and odd characters to form the scrambled / descrambled secret.
                        5) If the length of the secret is odd, remove the last character that is the added space.
                    """
                return secret[1::2] + secret[::2] if flag else (lambda l: b"".join(bytes([e]) + bytes([o]) for e, o in zip(secret[l:], secret[:l])) + (secret[-1:] if len(secret) % 2 else b""))((len(secret) + 1) // 2)
                #return (np.concatenate((np.frombuffer(secret, np.uint8)[1::2], np.frombuffer(secret, np.uint8)[::2]))
                #        if flag else
                #        np.frombuffer(secret, np.uint8).reshape(-1)[
                #            np.argsort(np.concatenate((np.arange((len(secret) + 1) // 2) * 2, np.arange(len(secret) // 2) * 2 + 1)))
                #        ]).tobytes()
            
            def circular_shift(secret: bytes, flag: bool = True) -> bytes:
                """
                Method 4: Circular Shift (Rotation)
                Scramble / Descramble the secret text by rotating the characters by a fixed number of positions
                Steps:
                    1) Get the shift number (2 if scrambling, 5 if descrambling).
                    2) Rotate the characters by the shift number.
                """
                s = 2 if flag else 5
                return b"".join(secret[i + l - s : i + l] + secret[i : i + 7 - s]if l == 7 else secret[i : i + 7] for i in range(0, len(secret), 7) for l in [len(secret[i : i + 7])])
            
            def zigzag_interleave(secret: bytes, flag: bool = True) -> bytes:
                """
                Method 5: Zigzag Interleave
                Scramble / Descramble the secret text by interleaving the characters in a zigzag pattern
                Steps:
                    1) If scrambling, interleave the even and odd characters to form the scrambled secret.
                    2) If descrambling, interleave the characters in a zigzag pattern to form the descrambled secret.
                """
                return secret[::2] + secret[1::2][::-1] if flag else bytes(secret[:(l := (len(secret) + 1) // 2)][i // 2] if i % 2 == 0 else secret[l:][::-1][i // 2] for i in range(len(secret)))
            
            def triplet_shuffle(secret: bytes, flag: bool = True) -> bytes:
                """
                Method 6: Swap Every 3 Characters
                Scramble / Descramble the secret text by swapping every 3 characters
                Steps:
                    1) If scrambling, split the secret into chunks of 3 characters and reverse the order of the chunks.
                    2) If descrambling, split the secret into chunks of 3 characters and reverse the order of the chunks.
                """
                pos = len(secret) % 3
                fill = (3 - pos) % 3 if not flag else 0
                padded = secret[:pos] + (b" " * fill if fill else b"") + secret[pos:] if not flag else secret
                chunks = [padded[i:i+3] for i in range(0, len(padded), 3)]
                if not flag:
                    chunks[0] = chunks[0][:3 - fill]
                return b"".join(chunks[::-1])
            
            def mirror_centre_reversal(secret: bytes, flag: bool = True) -> bytes:
                """
                Method 7: Mirror Centre Reversal
                Scramble / Descramble the secret text by reversing the order of the characters on both sides of the middle index
                Steps:
                    1) If scrambling, reverse the order of the characters on both sides of the middle index.
                    2) If descrambling, reverse the order of the characters on both sides of the middle index.
                """
                return secret[-(len(secret)//2):][::-1] + (bytes([secret[~(len(secret)//2)]]) if len(secret) % 2 else b"") + secret[:len(secret)//2][::-1]
                
            # Define a tuple to map the scrambling scheme to the corresponding method. The tuple is immutable and can be used as a dictionary.
            scramble_methods: tuple = (
                pairs_reversal,
                prime_index_shuffle,
                even_odd_split_swap,
                circular_shift,
                zigzag_interleave,
                triplet_shuffle,
                mirror_centre_reversal
            )

            # Call the appropriate scrambling method based on the scheme
            method: int = scheme % len(scramble_methods)
            return scramble_methods[method](secret, flag)
                
        def xor_bytes(a: bytes, b: bytes) -> bytes:
            """
            XOR two byte strings together.

            Args:
                a (bytes): The first byte string.
                b (bytes): The second byte string.

            Returns:
                bytes: The XORed byte string.
            """
            # Convert the byte strings to numpy arrays for efficient XOR operation
            arr_a: np.ndarray = np.frombuffer(a, dtype=np.uint8)
            arr_b: np.ndarray = np.frombuffer(b, dtype=np.uint8)
            return bytes(arr_a ^ arr_b)

        # Determine if the obfuscation is reversible or not based on the presence of the salt
        reversible: bool = salt is not None

        # Assign the secret to a variable for easier manipulation
        BSHSP_secret: bytes = secret

        # pre-hash the salt and pepper to create a hash map for faster access
        hashed_salts, hashed_peppers = hash_salt_and_pepper(salt)

        # Obfuscate/Deobfuscate the secret for the specified number of layers
        for _ in range(layers):
            if obfuscate_flag:
                # Obfuscation: Select a random scrambling scheme and scramble the secret
                BSHSP_masked_scheme: int = secrets.randbelow(1000) + 1
                BSHSP_scheme: int = BSHSP_masked_scheme % CryptoCore.BSHSP_SCHEMES
                BSHSP_secret: bytes = scramble(BSHSP_secret, BSHSP_scheme, flag=True)

                # Select a random pepper from the pre-defined pepper tuple
                pepper_masked_index: int = secrets.randbelow(1000) + 1
                pepper_index: int = pepper_masked_index % CryptoCore.PEPPER_TUPLE_COUNT
            else:
                # Deobfuscation: Extract the masked scheme and pepper index from the secret
                BSHSP_secret, masked_indices = BSHSP_secret[:-4], BSHSP_secret[-4:]
                BSHSP_masked_scheme, pepper_masked_index = struct.unpack("!HH", masked_indices)
                BSHSP_scheme: int = BSHSP_masked_scheme % CryptoCore.BSHSP_SCHEMES
                pepper_index: int = pepper_masked_index % CryptoCore.PEPPER_TUPLE_COUNT

            # Get the salt and pepper from the hash table
            salt = hashlib.sha256(hashed_salts[BSHSP_scheme]).digest()
            pepper = hashlib.sha256(hashed_peppers[pepper_index][BSHSP_scheme]).digest()

            # Shared logic: XOR the secret with the salt and pepper
            xor_base: bytes = (salt + pepper) * ((len(BSHSP_secret) // len(salt + pepper)) + 1)
            xored_bytes: bytes = xor_bytes(BSHSP_secret, xor_base[:len(BSHSP_secret)])

            if obfuscate_flag:
                # Obfuscation: Hash the XORed bytes if irreversible, otherwise append the masked scheme and pepper index
                hashed_bytes: bytes = xored_bytes if reversible else hashlib.sha256(xored_bytes).digest()
                BSHSP_secret: bytes = hashed_bytes + struct.pack("!HH", BSHSP_masked_scheme, pepper_masked_index) if reversible else hashed_bytes
            else:
                # Deobfuscation: Descramble the XORed bytes
                BSHSP_secret: bytes = scramble(xored_bytes, BSHSP_scheme, flag=False)

        return BSHSP_secret
        
    def __AES_GCM_decrypt(self, blob: bytes) -> Any:
        """
        Decrypts the data using AES-GCM and returns the original data.

        Args:
            blob (bytes): The encrypted data.

        Returns:
            Any: The original data. If the original data is a JSON string, it will be parsed into a dict or list. 
                 If the original data is not a JSON string, it will be returned as a string.
        """
        # Extract the salt, nonce, and ciphertext from the blob
        salt = blob[:self.block_size] # Extract the salt from the blob
        nonce = blob[self.block_size:self.block_size+12] # Extract the nonce from the blob
        ciphertext = blob[self.block_size+12:] # Extract the ciphertext from the blob

        # Decrypt the ciphertext with AES-GCM
        key = CryptoCore._CiphericTriadNode.derive_key(self.__ciphernode.extract_access_key(self.__PoAK, self.synapse.tag.encode("utf-8")), self.level, salt) # Derive the key from the salt using Argon2id2
        cipher = AESGCM(key) # Create a new AES cipher in GCM mode with the given key
        plaintext = cipher.decrypt(nonce, ciphertext, None).decode("utf-8") # Decrypt the ciphertext with the given nonce and associated data (None in this case)
        
        # Convert the data back to its original format (dict, list, or string)
        try:
            return json.loads(plaintext)
        except json.JSONDecodeError:
            return plaintext
        
    def __AES_GCM_encrypt(self, plaintext: Any) -> bytes:
        """
        Encrypts the data using AES-GCM and returns the encrypted data.

        Args:
            plaintext (Any): The data to encrypt. If it's a dict or list, it will be converted to a JSON string. 
                        If it's a string, it will be encoded to bytes.

        Returns:
            bytes: The encrypted data.
        """
        # Convert data to JSON string if dict/list, then to bytes
        if isinstance(plaintext, (dict, list)):
            plaintext = json.dumps(plaintext, separators=(',', ':'))  # compact JSON
        elif not isinstance(plaintext, str):
            plaintext = str(plaintext)
        plaintext_bytes = plaintext.encode("utf-8")

        # Encrypt the data with AES-GCM
        salt = os.urandom(self.block_size) # Generate a random salt
        key = CryptoCore._CiphericTriadNode.derive_key(self.__ciphernode.extract_access_key(self.__PoAK, self.synapse.tag.encode("utf-8")), self.level, salt)  # Derive the key from the salt
        nonce = os.urandom(12) # Generate a random nonce
        cipher = AESGCM(key) # Create a new AES cipher in GCM mode with the given key
        ciphertext = cipher.encrypt(nonce, plaintext_bytes, None) # Encrypt the data with the given nonce and associated data (None in this case)
        return salt + nonce + ciphertext # Combine the salt, nonce, and ciphertext into a single byte string

    #@neural_node()
    def GEP_encrypt(self, synapse: Synapse) -> Synapse:
        """
        Encrypts the data of permanent information and encodes it using genome-style encoding (A, T, C, G).
        
        Args:
            synapse (Synapse): The Synapse object containing the informatiom to encrypt. The data should be in the following format:
                {
                    "stimulus" (dict): The data to encrypt. If it's a dict or list, it will be converted to a JSON string.
                }
        
        Returns:
            Synapse: The updated Synapse object with encrypted and genome-encoded information.
                {
                    "response" (str): The genome-encoded encrypted response data.
                }

        """
        def _genome_encode(info_bytes: bytes) -> str:
            """
            Encode the encrypted data using genome-style encoding (A, T, C, G).

            Args:
                info_bytes (bytes): The encrypted data.

            Returns:
                str: The genome-encoded data.
            """
            # For each byte, get the 2-bit value of each of the 4 genome bases and map it to a genome base
            return "".join(
                self.GENOME_NUCLEOTIDES[(byte >> shift) & 0b11] # Map the 2-bit value to a genome base (A, T, C, G)
                for byte in info_bytes
                for shift in (6, 4, 2, 0)
            )
        #synapse.receive(str) # Ensure the stimulus is a string

        # Encrypt and encode the permanent information and store in the response
        synapse.signal["response"] = _genome_encode(self.__AES_GCM_encrypt(synapse.signal["stimulus"]))

        return synapse


    def GEP_decrypt(self, synapse: Synapse) -> Synapse:
        """
        Decrypts the genome-encoded information and decodes it back to original information.

        Args:
            synapse (Synapse): The Synapse object containing the genome-encoded information.
                {
                    "stimulus" (str): The genome-encoded encrypted information.
                }

        Returns:
            Synapse: The updated Synapse object with decrypted data.
                {
                    "response" (dict | str): The decrypted information. 
                        If the original data is a JSON string, it will be parsed into a dict or list. 
                        If the original data is not a JSON string, it will be returned as a string.
                }
        """
        def _genome_decode(genome: str) -> bytes:
            """
            Decode the genome-encoded data back into bytes.

            Args:
                genome (str): The genome-encoded data.

            Returns:
                bytes: The decoded data.

            Raises:
                ValueError: If the genome string length is not a multiple of 4 or if an invalid base is found in the genome string.
            """
            # Create a mapping from genome base to its 2-bit value            
            base_map = {base: idx for idx, base in enumerate(self.GENOME_NUCLEOTIDES)}

            # Check if the genome string length is a multiple of 4
            if len(genome) % 4 != 0:
                raise ValueError("Genome string length must be a multiple of 4")

            # For each 4-letter genome sequence, get the 2-bit value of each base and combine them into a byte
            data_bytes = bytes(
                sum(
                    base_map[base] << (6 - 2 * j) # Shift the 2-bit value to the correct position in the byte
                    for j, base in enumerate(genome[i:i+4])
                )
                for i in range(0, len(genome), 4)
            )
            return data_bytes
        synapse.receive(str) # Ensure the stimulus is a string

        # Decrypt and decode genome-encoded information and store in the response
        synapse.signal["response"] = self.__AES_GCM_decrypt(_genome_decode(synapse.signal["stimulus"]))
        return synapse
    
    def NEP_encrypt(self, synapse: Synapse) -> Synapse:
        """
        Encrypts the data of the neural stimulus and response and visualize with neural encoding spike train using base-8 width bars. 
        Each byte is converted to a spike train made of base-8 characters. Each base-8 digit (from 0 to 7) is represented by a distinct vertical bar symbol.

        Args:
            synapse (Synapse): The Synapse object containing the data to encrypt. 

        Returns:
            Synapse: The updated Synapse object with encrypted and neural encoded spike train.

        """
        def _to_bytes(data: Any) -> bytes:
            """
            Convert the data to bytes if it's a string or dict/list, otherwise return as is. 

            Args:
                data (Any): The data to convert.

            Returns:
                bytes: The data in bytes.
            """
            if isinstance(data, bytes):
                # If the data is already in bytes, return as is
                return data
            elif isinstance(data, (dict, list)):
                # If the data is a dict or list, convert to JSON string and then to bytes
                return json.dumps(data).encode("utf-8")
            elif isinstance(data, str):
                # If the data is a string, encode to bytes
                return data.encode("utf-8")
            else:
                # Otherwise, convert to string and then to bytes
                return str(data).encode("utf-8")
            
        def _rate_encode(data: Any) -> str:
            """
            Visualize the encrypted data with neural encoding spike train using base-8 width bars.

            Args:
                data (Any): The data to encrypt.
                If it's a dict or list, it will be converted to a JSON string.
                If it's a string, it will be encoded to bytes.

            Returns:
                str: The neural encoded spike train using varying width bars.
            """
            # Convert data to bytes if it's not already
            data = _to_bytes(data)

            # Convert bytes to binary string and add padding bits if necessary
            bits = "".join(f"{byte:08b}" for byte in data) # Convert each byte to 8-bit binary string and join them together
            bits += "0" * ((3 - len(bits) % 3) % 3) # Add padding bits to make the length a multiple of 3 (if necessary)

            # Group the bits into 3-bit groups and convert to base-8 spike train
            return "".join(
                self.NEURON_SPIKES[int(bits[i:i+3], 2)] # Get the corresponding bar for the 3-bit group
                for i in range(0, len(bits), 3)
            )
    
        # Encrypt neural signal with AES-GCM and encode it into neural spike train
        synapse.signal = _rate_encode(self.__AES_GCM_encrypt(synapse.signal))
        return synapse
    
    def NEP_decrypt(self, synapse: Synapse) -> Synapse:
        """
        Decode the neural encoded spike train and decrypts the data from the neural stimulus and response.

        Args:
            synapse (Synapse): The Synapse object containing the encrypted data. 

        Returns:
            Synapse: The updated Synapse object with decrypted data.

        Raises:
            ValueError: If the spike train contains invalid characters.
        """
        def _from_bytes(data: bytes) -> Any:
            """
            Convert the data back to its original format (dict, list, or string).
            If it's a JSON string, it will be parsed into a dict or list. 
            If it's not a JSON string, it will be returned as a string.
            If it's not in UTF-8, it will be returned as bytes.

            Args:
                data (bytes): The data to convert.

            Returns:
                Any: The data in its original format.
            """
            try:
                # Try to decode the bytes as UTF-8 and then as JSON
                text = data.decode("utf-8")
                try:
                    return json.loads(text)
                except json.JSONDecodeError:
                    return text
            except UnicodeDecodeError:
                # If the data is not in UTF-8, return as bytes
                return data
            
        def _rate_decode(spike_train: str) -> bytes:
            """
            Decode a base-8 vertical bar spike train back into the original bytes.

            Args:
                spike_train (str): The encoded spike train using vertical bars.

            Returns:
                bytes: The original encrypted byte sequence.
            """
            # Convert spike train to binary string and remove padding bits at the end if necessary
            spike_to_bits = {spike: f"{idx:03b}" for idx, spike in enumerate(self.NEURON_SPIKES)}
            try:
                bits = "".join(spike_to_bits[spike] for spike in spike_train) # Convert each spike to 3-bit binary string and join them together
            except KeyError as e:
                raise ValueError(f"Invalid spike symbol in train: {e}") # Raise an error if the spike train contains invalid characters
            bits = bits[:len(bits) - (len(bits) % 8)] # Remove padding bits at the end if necessary

            # Convert every 8 bits into 8-bit byte and return original data format
            return _from_bytes(bytes(
                int(bits[i:i+8], 2) # Get the byte value from the 8-bit binary string
                for i in range(0, len(bits), 8)
            ))

        # Decode the data from neural spike train and decrypt neural signal using AES-GCM encryption algorithm
        synapse.signal = self.__AES_GCM_decrypt(_rate_decode(synapse.signal))
        return synapse

    class _CiphericTriadNode:
        """
        Cipheric Triad Node (CTN) is the mechanism for accessing and managing the keys for encryption and decryption. 
        It is responsible for generation and management of the encryptions for perpetual and periodic access.
        It uses a 3-key locking (Cipheric Triad) mechanism:
            - System-Base Key (S-Key) - derived from the system's serial number.
            - Human-Base Key (H-Key) - derived from the human's ID number.
            - Dynamic Key (D-Key) - derived from the tag of the synapse object.
        These 3 triad keys are combined in access keychains, one for Perpetual Access (PeAK) and the other for Periodic Access (PoAK).
        
        Functions:
            - Forge the 3 triad keys: System-Base Key, Human-Base Key and Dynamic Key from the identifiers using multi-threading.
            - Scramble and hash the triad keys using BSH/SP mechanism.
            - Assemble the triad keys into access keychains Perpetual Access Key (PeAK) or Periodic Access Key (PoAK).
            - Retrieve the original triad keys for encryption or decryption.
        
        Attributes:
            synapse (Synapse): The Synapse object containing the data that requires the encryption or decryption key.

        Methods:
            # --- Constructor ---
            __init__(synapse: Synapse) -> None:
                Pass the synapse to be encrypted/decrypted to the Cipheric Triad Node to generate the encryption/decryption key.
                       
            # --- Static Methods ---
            derive_key(password: str, salt: bytes) -> bytes:
                Derives a key from the password and salt using Argon2id.
            extract_access_key() -> list[bytes]:
                Extract the Cipheric Triad keys from the Perpetual Access Key (PeAK) or Periodic Access Key (PoAK).

            # --- Public Methods ---
            gen_access_key() -> bytes:
                Generate the Cipheric Triad keys into Perpetual Access Key (PeAK) or Periodic Access Key (PoAK).
                
            # --- Private Methods ---
            __forge_triad_key() -> list[bytes]:
                Forge the keys for Cipheric Triad 3-key locking mechanism: System-Base Key, Human-Base Key and Dynamic Key.
        """
        # --- Constructor ---
        def __init__(self, synapse: Synapse): 
            """
            Pass the synapse to be encrypted/decrypted to the Cipheric Triad Node to generate the encryption/decryption key.

            Args:
                synapse (Synapse): The Synapse object containing the data that requires the encryption or decryption key.
            """
            self.synapse = synapse          # The Synapse object containing the data to encrypt.
           
        # --- Static Methods ---
        @staticmethod
        def derive_key(secret: bytes, level: Literal["Lo", "Med", "Hi"], salt: bytes, pepper: Optional[bytes] = secrets.token_bytes(32)) -> bytes:
            """
            Derives a key from the secret, salt, and pepper using Argon2id.

            Args:
                secret (bytes): The secret to use for key derivation.
                salt (bytes): The salt to use for key derivation.
                pepper (bytes): The pepper to use for key derivation. If None, only the salt will be used.

            Returns:
                bytes: The derived key.
            """
            # Assign the Argon2id parameters according to the security level
            match level:
                case "Lo": time_cost, memory_cost, parallelism = 3, 12*1024, 2  # Low security level: 3 iterations, 12 MB, 2 threads
                case "Med": time_cost, memory_cost, parallelism = 3, 2**16, 4   # Medium security level: 3 iterations, 64 MB, 4 threads           
                case "Hi": time_cost, memory_cost, parallelism = 5, 2**17, 8    # High security level: 5 iterations, 128 MB, 8 threads

            return hash_secret_raw(
                secret=secret + pepper,     # Add pepper to the secret to increase the entropy of the secret
                salt=salt,                  # Use the provided salt with pepper, if provided
                time_cost=time_cost,        # number of iterations in the constructor
                memory_cost=memory_cost,    # Use the memory cost in kb in the constructor
                parallelism=parallelism,    # Use the number of threads in the constructor
                hash_len=32,                # Use the key length specified in the constructor
                type=Type.ID                # Argon2id key derivation function
            )
        
        @staticmethod
        def extract_access_key(access_key: bytes, dynamic_key: bytes) -> bytes:
            """
            Extract the actual encryption keychain from the Perpetual Access Key (PeAK) or Periodic Access Key (PoAK).
            - Reverse the final BSH/SP obfuscation of the keychain.
            - For PoAK, return the keychain. 
            - For PeAK, reverse the BSH/SP obfuscation of the keychain and split the keychain into system-base key and human-base key.
            
            Args:
                access_key (bytes): The access key to retrieve.
                dynamic_key (bytes): The dynamic key to retrieve the access key.

            Returns:
                bytes: The joined keychain of PeAK or PoAK.
            """          
            # Step 1: Reverse the final BSH/SP scrambling of the keychain
            joined_keychain: bytes = CryptoCore._BSHSP_obfuscation(access_key, dynamic_key, obfuscate_flag=False)
                        
            # Step 2: Return the keychain (for PoAK)
            key_len = (64 + CryptoCore.BSHSP_LAYERS * 8) // 2
            if len(joined_keychain) > key_len * 2:
                return joined_keychain
            
            # Step 3: Split and reverse the keys in the keychain at the same time (for PeAK only)
            system_base_key, human_base_key = joined_keychain[:key_len], joined_keychain[key_len:]
            key_chain = list(_thread_pool.map(
                lambda key: CryptoCore._BSHSP_obfuscation(key, dynamic_key, obfuscate_flag=False),
                [system_base_key, human_base_key]
            ))            
            return b"".join(key_chain)
        
        # --- Public Methods ---
        def gen_access_key(self, key_type: Literal["PeAK", "PoAK"]) -> bytes:
            """
            Generate Perpetual Access Key (PeAK) or Periodic Access Key (PoAK) from the Triad keychain.
            - Forge the triad keychain: system-base key, human-base key and dynamic key.
            - Scramble and hash the keychain using BSH/SP mechanism.
            - Generate a single access key from the triad keychain with defined mechanism according to the key type.
            - Scramble and hash the assembled access key using BSH/SP mechanism.

            Args:
                key_type (Literal["PeAK", "PoAK"]): The type of key to generate.
                    - "PeAK" for Perpetual Access Key (PeAK) - for encryption and decryption of long-term / permanent data (eg. persona, memory database)
                    - "PoAK" for Periodic Access Key (PoAK) - for encryption and decryption of short-term / temporary data (eg. neural signals)

            Returns:
                bytes: The generated access key.
            """          
            # Step 1: Forge the triad keychain: system-base key, human-base key and dynamic key and assemble into keychain
            keychain: list[bytes] = self.__forge_triad_key() # Forge the triad keychain: system-base key, human-base key and dynamic key
            if key_type == "PeAK": keychain.pop()  # Remove the dynamic key from the keychain if PeAK
            dynamic_key: bytes = self.synapse.tag.encode("utf8") if key_type == "PeAK" else keychain[2] # Use the tag as the dynamic key if PeAK, otherwise use the dynamic key from the keychain

            # Step 2: Scramble and hash all keys in the keychain using BSH/SP mechanism at the same time with multi-threading
            keychain = list(_thread_pool.map(lambda key: CryptoCore._BSHSP_obfuscation(secret=key, salt=dynamic_key), keychain))

            # Step 3: Assemble the keychain into a single access key and scramble it using BSH/SP mechanism
            access_key: bytes = CryptoCore._BSHSP_obfuscation(secret=b"".join(keychain), salt=self.synapse.tag.encode("utf8") )
            return access_key

        # --- Private Methods ---
        def __forge_triad_key(self) -> list[bytes]:
            """
            Forge the 3 Cipheric Triad keys from the specified identifiers using the BSH/SP obfuscation mechanism.
            - System-Base Key is forged from the system's serial number.
            - Human-Base Key is forged from the human's ID number.
            - Dynamic Key is forged from the tag of the synapse object.

            Args:
                None

            Returns:
                list[bytes]: The forged triad keychain.
            """              
            def forge_key(key: bytes, salt: Optional[bytes] = None) -> bytes:
                """
                Forge a key from the specified identifier using the BSH/SP obfuscation mechanism.

                Args:
                    key (bytes): The identifier to forge the key.
                    salt (bytes, Optional): The salt to use for the key derivation. If None, no salt is used.

                Returns:
                    bytes: The forged key.                
                """
                return CryptoCore._BSHSP_obfuscation(
                            secret=CryptoCore._CiphericTriadNode.derive_key(
                                secret=key, 
                                level= "Hi" if salt else "Lo",
                                salt=salt
                            ) if salt else key)
            
            # Forge the 3 triad keys: system-base key, human-base key and dynamic key from the identifiers using multi-threading
            return list(_thread_pool.map(lambda args: forge_key(*args), [
                (SYSTEM_BASE_IDENTIFIER, SERIAL_NUMBER.encode("utf8")), # Forge system-base key from serial number
                (human_base_identifier, human_id.encode("utf8")), # Forge human-base key from human ID
                (self.synapse.tag.encode("utf8"), None), # Forge dynamic key from the synapse tag
            ]))