{{- if .System -}}
<|im_start|>system
{{ .System }}<|im_end|>
{{- end -}}
{{- range $i, $_ := .Messages }}
{{- $last := eq (len (slice $.Messages $i)) 1 -}}
{{- if eq .Role "user" }}
<|im_start|>user
{{ .Content }}<|im_end|>
{{- else if eq .Role "assistant" }}
<|im_start|>assistant
{{ if .Content }}{{ .Content }}{{ if not $last }}<|im_end|>
{{- else -}}<|im_end|>{{- end -}}
{{- end -}}
{{- end -}}
{{- if and (ne .<PERSON> "assistant") $last }}
<|im_start|>assistant
{{ end -}}
{{- end }}