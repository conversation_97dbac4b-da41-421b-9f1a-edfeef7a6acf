import body, heart, brain

ai_persona = "AIVAHr"
body.encrypt_body_module(ai_persona)
heart.encrypt_heart_module(ai_persona)

ai_persona = "Grace"
body.encrypt_body_module(ai_persona)
heart.encrypt_heart_module(ai_persona)

ai_persona = "Joyce"
body.encrypt_body_module(ai_persona)
heart.encrypt_heart_module(ai_persona)

ai_persona = "User"
body.encrypt_body_module(ai_persona)

command = "emotion_check"
brain.encrypt_command(command)

command = "memory_classify"
brain.encrypt_command(command)