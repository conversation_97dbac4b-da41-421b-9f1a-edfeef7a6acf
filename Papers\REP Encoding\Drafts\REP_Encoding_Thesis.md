# REP Encoding: A Neural-Inspired Encoding Method for Uniform Communication between Neural Nodes

## Abstract

Rate Encoded Pulse (REP) Encoding is a communication scheme designed to transmit stimulus and response signals between modules or nodes by using a symbolic representation inspired by biological neurons. REP Encoding mimics the rate coding used in neural synapses between axons and dendrites, representing information as sequences of "spikes." In this paper, we describe an implementation of REP Encoding using base-8 symbolic spikes, represented by 8 distinct Unicode vertical bars of different widths.

## 1. Introduction

Neurons in the human brain communicate using spikes—brief electrical impulses. One common coding scheme is rate coding, where the frequency of spikes encodes the information. REP Encoding draws on this idea, applying symbolic spike trains to encode digital information for transmission in AI systems or modular architectures.

To ensure both consistency and clarity, each byte of data is converted to a spike train made of base-8 characters. Each base-8 digit (from 0 to 7) is represented by a distinct vertical bar symbol.

## 2. Symbol Set for Base-8 Encoding

We choose 8 Unicode box drawing characters that differ by width or visual density to represent octal digits 0–7.

| Octal | Symbol | Unicode Code Point | Description                |
| ----- | ------ | ------------------ | -------------------------- |
| 0     |        | U+2003             | EM SPACE (invisible)       |
| 1     | ▁      | U+2581             | Lower One Eighth Block     |
| 2     | ▂      | U+2582             | Lower One Quarter Block    |
| 3     | ▃      | U+2583             | Lower Three Eighths Block  |
| 4     | ▄      | U+2584             | Lower Half Block           |
| 5     | ▅      | U+2585             | Lower Five Eighths Block   |
| 6     | ▆      | U+2586             | Lower Three Quarters Block |
| 7     | ▇      | U+2587             | Lower Seven Eighths Block  |

(Note: U+2588 FULL BLOCK may be reserved for future GEP work.)

## 3. Encoding Process

1. Input data is converted to a byte string (e.g., using UTF-8 or ASCII).
2. Each byte is split into 3 octal digits (since 8^3 = 512 > 256).
3. Each octal digit is mapped to a specific vertical bar character.
4. The resulting spike train string is used for communication.

### Example

Let us encode the ASCII character `A`:

* ASCII value of `A` is 65.
* Convert 65 to octal: `101`.
* Map to spikes:

  * `1` → ▁
  * `0` →  
  * `1` → ▁
* Spike train: `▁ ▁`

Another example, encoding ASCII `Z` (value 90):

* Octal: `132`
* Spike train: `▁▃▂`

### Full Example String

Encoding the string "AZ":

* `A` → `▁ ▁`
* `Z` → `▁▃▂`
* Combined train: `▁ ▁▁▃▂`

## 4. Decoding

To decode:

1. Read spike train in chunks of 3 characters.
2. Map each character back to octal digits 0–7.
3. Convert the 3-digit octal number to decimal (byte).
4. Reconstruct original string.

## 5. Benefits

* **Visual**: Easy to visualize and debug with human-readable symbols.
* **Compact**: Octal encoding is more compact than binary (3 vs 8 symbols per byte).
* **Inspired by biology**: Mimics neural rate coding for symbolic alignment.

## 6. Limitations and Considerations

* Font rendering must support block characters.
* Visual ambiguity may occur across platforms.
* Not suitable for cryptographic security by itself.

## 7. Applications

* Symbolic AI communication between synaptic-like modules.
* Debuggable message-passing between systems.
* Bio-inspired low-level interface between modular nodes.

## 8. Future Work

A companion system, Genome Encoded Pulse (GEP), will handle long-term or characteristic-based static storage. GEP will use amino acid or DNA codon representation for persistent memory.

## 9. Conclusion

REP Encoding introduces a novel symbolic scheme for inter-module communication inspired by the brain's synaptic signaling. Its use of base-8 vertical bar characters provides both compactness and clarity, paving the way for bio-inspired symbolic AI frameworks.

---

*Prepared by: \[Author Placeholder]*
*Date: June 2025*
