from pydoc import text
import secrets
from sympy import primerange
import numpy as np
prime_indices = set(primerange(0, 1))

def scramble(secret: bytes, scheme: int, flag: bool = True) -> bytes:
            """
            Scramble the secret bytes using the specified scrambling scheme.

            Args:
                secret (bytes): The secret bytes to scramble.
                scheme (int): The scrambling scheme to use.
                flag (bool): Whether to execute scrambling (True) or descrambling (False). Default is True.

            Returns:
                bytes: The scrambled or descrambled secret bytes.
            """       
            def pairs_reversal(secret: bytes, flag: bool = True) -> bytes:
                """
                Method 1: Pairs Reversal
                Scramble / Descramble the secret bytes by reversing the order of the character pairs and joining them in reverse order
                Steps:
                    1) Split the secret into pairs of characters.
                    2) Reverse the order of the pairs.
                    3) Join the pairs in reverse order to form the scrambled / descrambled secret.
                """
                return b"".join([secret[i:i+2] for i in range(0, len(secret), 2)][::-1])
            
            def prime_index_shuffle(secret: bytes, flag: bool = True) -> bytes:
                """
                    Method 2: Index Shuffle Based on Prime Positions
                    Scramble / Descramble the secret bytes by shuffling the characters based on prime positions
                    Steps:
                        1) Get the prime and non-prime positions.
                        2) If scrambling, shuffle the characters based on prime positions.
                        3) If descrambling, put the characters back in their original positions.
                        4) Join the characters to form the scrambled / descrambled secret.
                    """
                prime_set = set(primerange(0, len(secret)))
                primes = [i for i in range(len(secret)) if i in prime_set]
                non_primes = [i for i in range(len(secret)) if i not in prime_set]

                if flag:
                    return bytes(secret[i] for i in primes + non_primes)
                else:
                    prime_part = secret[:len(primes)]
                    non_prime_part = secret[len(primes):]
                    original = bytearray(len(secret))
                    for idx, i in enumerate(primes):
                        original[i] = prime_part[idx]
                    for idx, i in enumerate(non_primes):
                        original[i] = non_prime_part[idx]
                    return bytes(original)
            
            def even_odd_split_swap(secret: bytes, flag: bool = True) -> bytes:
                """
                Method 3: Even-Odd Split and Swap
                Scramble / Descramble the secret bytes by interleaving the even and odd characters
                Steps:
                    1) Get the half index (rounded up).
                    2) Get the odd characters (from the half index to the end).
                    3) Get the even characters (from the beginning to the half index).
                        4) Interleave the even and odd characters to form the scrambled / descrambled secret.
                        5) If the length of the secret is odd, remove the last character that is the added space.
                    """
                return secret[1::2] + secret[::2] if flag else (lambda l: b"".join(bytes([e]) + bytes([o]) for e, o in zip(secret[l:], secret[:l])) + (secret[-1:] if len(secret) % 2 else b""))((len(secret) + 1) // 2)
            
            def circular_shift(secret: bytes, flag: bool = True) -> bytes:
                """
                Method 4: Circular Shift (Rotation)
                Scramble / Descramble the secret text by rotating the characters by a fixed number of positions
                Steps:
                    1) Get the shift number (2 if scrambling, 5 if descrambling).
                    2) Rotate the characters by the shift number.
                """
                s = 2 if flag else 5
                return b"".join(secret[i + l - s : i + l] + secret[i : i + 7 - s]if l == 7 else secret[i : i + 7] for i in range(0, len(secret), 7) for l in [len(secret[i : i + 7])])
            
            def zigzag_interleave(secret: bytes, flag: bool = True) -> bytes:
                """
                Method 5: Zigzag Interleave
                Scramble / Descramble the secret text by interleaving the characters in a zigzag pattern
                Steps:
                    1) If scrambling, interleave the even and odd characters to form the scrambled secret.
                    2) If descrambling, interleave the characters in a zigzag pattern to form the descrambled secret.
                """
                return secret[::2] + secret[1::2][::-1] if flag else bytes(secret[:(l := (len(secret) + 1) // 2)][i // 2] if i % 2 == 0 else secret[l:][::-1][i // 2] for i in range(len(secret)))
            
            def triplet_shuffle(secret: bytes, flag: bool = True) -> bytes:
                """
                Method 6: Swap Every 3 Characters
                Scramble / Descramble the secret text by swapping every 3 characters
                Steps:
                    1) If scrambling, split the secret into chunks of 3 characters and reverse the order of the chunks.
                    2) If descrambling, split the secret into chunks of 3 characters and reverse the order of the chunks.
                """
                pos = len(secret) % 3
                fill = (3 - pos) % 3 if not flag else 0
                padded = secret[:pos] + (b" " * fill if fill else b"") + secret[pos:] if not flag else secret
                chunks = [padded[i:i+3] for i in range(0, len(padded), 3)]
                if not flag:
                    chunks[0] = chunks[0][:3 - fill]
                return b"".join(chunks[::-1])
            
            def mirror_centre_reversal(secret: bytes, flag: bool = True) -> bytes:
                """
                Method 7: Mirror Centre Reversal
                Scramble / Descramble the secret text by reversing the order of the characters on both sides of the middle index
                Steps:
                    1) If scrambling, reverse the order of the characters on both sides of the middle index.
                    2) If descrambling, reverse the order of the characters on both sides of the middle index.
                """
                return secret[-(len(secret)//2):][::-1] + (bytes([secret[~(len(secret)//2)]]) if len(secret) % 2 else b"") + secret[:len(secret)//2][::-1]
                
            # Define a dictionary to map the scrambling scheme to the corresponding method
            scramble_methods = {
                1: pairs_reversal,
                2: prime_index_shuffle,
                3: even_odd_split_swap,
                4: circular_shift,
                5: zigzag_interleave,
                6: triplet_shuffle,
                7: mirror_centre_reversal
            }

            # Call the appropriate scrambling method based on the scheme
            method = (scheme % 7) + 1
            return scramble_methods[method](secret, flag)



def main():

    bytes_text = b"The quick brown fox jumps over the lazy dog." # Convert the string to bytes using the encode() method.
    scrambled_text = scramble(bytes_text, True) # Pass the bytes to the scramble function.
    descrambled_text = scramble(scrambled_text, False)
    print(f"Original text: {bytes_text}") # Print the original bytes.
    print(f"Scrambled text: {scrambled_text}") # Print the scrambled bytes.
    print(f"Descrambled text: {descrambled_text}") # Print the descrambled bytes.
    print(f"Match: {bytes_text == descrambled_text}") # Compare the original bytes to the descrambled bytes.
    print()
    mismatch = 0
    for i in range(5_000):
        scheme = secrets.randbelow(1000) % 7 + 1
        print(scheme)
        long_text = secrets.token_bytes(1024)
        scrambled_text = scramble(long_text, scheme, True)
        descrambled_text = scramble(scrambled_text, scheme, False)
        if long_text != descrambled_text:
            mismatch += 1

    print(f"Mismatch %: {mismatch / 1_000_000 * 100:.2f}%")

if __name__ == "__main__":
    main()