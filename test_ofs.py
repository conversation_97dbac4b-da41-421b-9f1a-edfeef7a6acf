import hashlib
import numpy as np
import os
import random
import secrets
from sympy import primerange
from functools import lru_cache

BSHSP_SCHEMES: int = 1                  # Number of scrambling schemes for BSH/SP obfuscation
BSHSP_LAYERS: int = 777              # Number of iterations for BSH/SP obfuscation (34 is the magic number)    

PEPPER_TUPLE: tuple[str] = (            # Tuple of peppers for key derivation
    "Eph2_8_SavedByGraceThroughFaith",  # Eph 2:8 For by grace you have been saved through faith. And this is not your own doing; it is the gift of God,
    "1John4_10_GodLovedUsFirst",        # 1 John 4:10 In this is love, not that we have loved God but that he loved us and sent his Son to be the propitiation for our sins.
    "2Cor12_9_GraceIsSufficient",       # 2 Cor 12:9 But he said to me, “My grace is sufficient for you, for my power is made perfect in weakness.” Therefore I will boast all the more gladly of my weaknesses, so that the power of Christ may rest upon me.
    "Titus2_11_GraceAppearedToSaveAll", # Titus 2:11 For the grace of God has appeared, bringing salvation for all people,
    "1Pet5_10_GodOfAllGraceRestores",   # 1 Pet 5:10 And after you have suffered a little while, the God of all grace, who has called you to his eternal glory in Christ, will himself restore, confirm, strengthen, and establish you.
    "Heb4_16_ThroneOfGrace",            # Heb 4:16 Let us then with confidence draw near to the throne of grace, that we may receive mercy and find grace to help in time of need.
    "Rom5_15_GraceAboundsInChrist"      # Rom 5:15 But the free gift is not like the trespass. For if many died through one man's trespass, much more have the grace of God and the free gift by the grace of that one man Jesus Christ abounded for many.
)
PEPPER_TUPLE_COUNT: int = len(PEPPER_TUPLE) # Number of peppers in the tuple

@lru_cache(maxsize=None)
def _BSHSP_obfuscation(secret: bytes, 
                        salt: bytes = None, 
                        layers: int = BSHSP_LAYERS) -> bytes:
    """
    Obfuscate the secret text by random scrambling scheme, XORing with salt and pepper, and hashing with SHA-256, for a specified number of iterations.
    This mechanism is called Breakfast Scrambled Hash with Salt and Pepper (BSH/SP).

    Args:
        secret (bytes): The secret text to scramble and hash with salt and pepper.
        reversible (bool): Whether the obfuscation is reversible or not.
            - If True, the salt and pepper are derived from deterministic factors, making the obfuscation reversible.
            - If False, the salt and pepper are randomly generated, making the obfuscation irreversible.
        layers (int): The number of layers iterations to perform the obfuscation. Default is BSHSP_LAYERS.

    Returns:
        bytes: The secret after scrambling and hashing with salt and pepper.
    """
    def obfuscate(secret: str, scheme: int) -> str:
        """
        Obfuscate the secret text using the specified scrambling scheme.

        Args:
            secret (str): The secret text to obfuscate.
            scheme (int): The scrambling scheme to use.

        Returns:
            str: The obfuscated secret text.
        """
        match scheme % 7 + 1:
            case 1: # Method 1: Pairs Reversal
                return "".join([secret[i:i+2][::-1] for i in range(0, len(secret), 2)][::-1])
            case 2: # Method 2: Index Shuffle Based on Prime Positions
                # Get the prime and non-prime positions
                prime_set = set(primerange(0, len(secret)))
                indices = list(range(len(secret)))
                primes, non_primes = [], []

                # Shuffle the characters based on prime positions
                [(primes if i in prime_set else non_primes).append(i) for i in indices]

                # If flag is True, then scramble the secret:
                # Scramble the secret text by shuffling the characters based on prime positions
                chars = list(secret)                                                # Convert the secret to a list of characters
                shuffled = [chars[i] for i in primes + non_primes]                  # Shuffle the characters based on prime positions
                return ''.join(shuffled)                                            # Join the shuffled characters to form the scrambled secret
            case 3: # Method 3: Even-Odd Split and Swap
                # Scramble the secret text by interleaving the even and odd characters
                return secret[1::2] + secret[::2] # Interleave the even and odd characters to form the scrambled secret
            case 4: # Method 4: Circular Shift (Rotation)
                # Scramble the secret text by rotating the characters by a fixed number of positions
                pos: int = 3 % len(secret)          # Rotate by 3 positions
                return secret[pos:] + secret[:pos]  # Join the rotated characters to form the scrambled secret
            case 5: # Method 5: Zigzag Obfuscation
                # Scramble the secret text by interleaving the characters in a zigzag pattern
                chars: list[str] = list(secret)                 # Convert the secret to a list of characters
                return ''.join(chars[::2] + chars[1::2][::-1])  # Interleave the characters in a zigzag pattern to form the scrambled secret
            case 6: # Swap Every 3 Characters
                # Scramble the secret text by swapping every 3 characters
                chunks: list[str] = [secret[i:i+3] for i in range(0, len(secret), 3)]  # Split the secret into chunks of 3 characters
                return ''.join(chunks[::-1]) # Reverse the order of the chunks to form the scrambled secret
            case 7: # Method 7: Mirror Center Obfuscation
                # Scramble the secret text by reversing the order of the characters on both sides of the middle index
                mid: int = len(secret) // 2                         # Get the middle index
                if len(secret) % 2 == 0:
                    return secret[mid:][::-1] + secret[:mid][::-1]  # Reverse the order of the characters on both sides of the middle index
                else:
                    return secret[mid+1:][::-1] + secret[mid] + secret[:mid][::-1] # Reverse the order of the characters on both sides of the middle index, excluding the middle character                       
                
    def xor_bytes(a: bytes, b: bytes) -> bytes:
        """
        XOR two byte strings together.

        Args:
            a (bytes): The first byte string.
            b (bytes): The second byte string.

        Returns:
            bytes: The XORed byte string.
        """
        # Convert the byte strings to numpy arrays for efficient XOR operation
        arr_a = np.frombuffer(a, dtype=np.uint8)
        arr_b = np.frombuffer(b, dtype=np.uint8)
        return bytes(arr_a ^ arr_b)

    # Determine if the obfuscation is reversible or not based on the presence of the salt
    reversible = salt is not None

    # Initialize the secret as a string to avoid any unintended type conversion or mutation
    BSHSP_secret: str = secret.decode("latin1") # Decode the secret from bytes to string

    # Scramble and hash the secret for the specified number of layers
    for i in range(layers):

        # Generate the salt and pepper based on the reversible flag        
        if reversible:               
            # Select a random pepper from the pre-defined pepper tuple
            pepper_masked_index: int = random.randint(0, 999) # Select a random masked index for the pepper
            pepper_index: int = pepper_masked_index % PEPPER_TUPLE_COUNT # Unmask the pepper index
            pepper: bytes = PEPPER_TUPLE[pepper_index].encode() # Get the pepper bytes from the tuple              
        else:
            salt: bytes = os.urandom(32) # Generate a random salt for irreversible obfuscation
            pepper: bytes = secrets.token_bytes(32) # Generate a random pepper for irreversible obfuscation

        # Hash the salt and pepper for the masked index number of times
        for _ in range(pepper_index+1):
            hash_salt: bytes = hashlib.sha256(salt).digest()
            hash_pepper: bytes = hashlib.sha256(pepper).digest()

        # Scramble the key by reversing the order of the characters, XORing with salt and pepper, and hashing with SHA-256.
        BSHSP_masked_scheme: int = random.randint(0, 999) # Select a random masked index for the BSHSP scheme
        BSHSP_scheme: int = BSHSP_masked_scheme % BSHSP_SCHEMES # Unmask the BSHSP scheme
        BSHSP_scheme = 1
        scrambled_text: str = obfuscate(BSHSP_secret, BSHSP_scheme) # Scramble the secret using the selected scheme

        # XOR the scrambled key with the salt and pepper
        xor_base: bytes = (hash_salt + hash_pepper) * ((len(scrambled_text) // len(hash_salt + hash_pepper)) + 1) # Create a base for XORing that is at least as long as the scrambled text
        xored_bytes = xor_bytes(scrambled_text.encode("latin1"), xor_base[:len(scrambled_text)]) # XOR the scrambled text with the salt and pepper to obfuscate the secret further.

        # Keep the xored bytes as is if reversible obfuscation is used, otherwise hash with SHA-256
        hashed_text = xored_bytes.decode("latin1") if reversible else hashlib.sha256(xored_bytes).hexdigest()
        BSHSP_secret = f"{hashed_text}{BSHSP_masked_scheme:03}{pepper_masked_index:03}" if reversible else hashed_text

    obfuscated_secret: bytes = BSHSP_secret.encode("latin1") # Encode the secret back to bytes before returning it.
    return obfuscated_secret if reversible else obfuscated_secret
    
@lru_cache(maxsize=None)
def _BSHSP_deobfuscation(obfuscated: bytes, salt: bytes, layers: int = BSHSP_LAYERS) -> bytes:
    """
    Reverse the BSHSP obfuscation if reversible=True was used during encoding.
    
    Args:
        obfuscated (bytes): The obfuscated data returned from _BSHSP_obfuscation.
        salt (bytes): The same salt used during the obfuscation.
        layers (int): The number of layers used during the obfuscation (must match original).

    Returns:
        bytes: The original secret that was obfuscated.
    """

    def deobfuscate(secret: str, scheme: int) -> str:
        match scheme % 7 + 1:
            case 1:  # Pairs Reversal (reverse of reverse is original)
                return "".join([secret[i:i+2][::-1] for i in range(0, len(secret), 2)][::-1])
            case 2:  # Index Shuffle Based on Prime Positions
                # Get the prime and non-prime positions
                prime_set = set(primerange(0, len(secret)))
                indices = list(range(len(secret)))
                primes, non_primes = [], []

                # Shuffle the characters based on prime positions
                [(primes if i in prime_set else non_primes).append(i) for i in indices]

                # Get the parts from the scrambled string
                prime_part = secret[:len(primes)]
                non_prime_part = secret[len(primes):]

                # Create an empty list to hold characters in original order
                original = [''] * len(secret)

                # Put characters back in their original positions
                for idx, i in enumerate(primes):
                    original[i] = prime_part[idx]
                for idx, i in enumerate(non_primes):
                    original[i] = non_prime_part[idx]

                return ''.join(original)              
            case 3:  # Even-Odd Split and Swap
                #add space to the middle if the length of the secret is odd
                even_secret = secret[:len(secret) // 2] + "@" + secret[len(secret) // 2:] if len(secret) % 2 != 0 else secret
                half = len(even_secret) // 2 if len(even_secret) % 2 == 0 else (len(even_secret) + 1) // 2
                odd_chars = even_secret[:half]
                even_chars = even_secret[half:]
                result = []
                for o, e in zip(odd_chars, even_chars):
                    result.append(e)
                    result.append(o)
                # If one is longer (odd length), append the remaining
                if len(odd_chars) > len(even_chars):
                    result.append(odd_chars[-1])
                # If odd, remove the last character that is the added space
                if len(secret) % 2 != 0:
                    result.pop()
                return ''.join(result)        
            case 4:  # Circular Shift (Rotation)
                pos = 3 % len(secret)
                return secret[-pos:] + secret[:-pos]
            case 5:  # Zigzag Obfuscation
                n = len(secret)
                half = (n + 1) // 2
                even_part = secret[:half]            # chars[::2]
                odd_part_reversed = secret[half:]    # reversed(chars[1::2])
                odd_part = odd_part_reversed[::-1]      # chars[1::2]
                
                # Interleave
                result = []
                for i in range(n):
                    if i % 2 == 0:
                        result.append(even_part[i // 2])
                    else:
                        result.append(odd_part[i // 2])
                return ''.join(result)
            case 6:  # Swap Every 3 Characters (reverse of reverse is original)
                    # add one or two space after the first character if the remainder of the length of the secret is 2 or 1
                    pos = len(secret) % 3
                    fill = (3 - (len(secret) % 3)) % 3
                    even_secret = secret[:pos] + "@" * fill + secret[pos:]
                    chunks = [even_secret[i:i+3] for i in range(0, len(even_secret), 3)]
                    # remove the filled characters at the end
                    chunks[0] = chunks[0][:3-fill]
                    return ''.join(chunks[::-1])
            case 7:  # Mirror Center Obfuscation                       
                # Deobfuscate the secret text by reversing the order of the characters on both sides of the middle index 
                mid: int = len(secret) // 2                         # Get the middle index
                if len(secret) % 2 == 0:
                    return secret[mid:][::-1] + secret[:mid][::-1]  # Reverse the order of the characters on both sides of the middle index
                else:
                    return secret[mid+1:][::-1] + secret[mid] + secret[:mid][::-1] # Reverse the order of the characters on both sides of the middle index, excluding the middle character
                
    def xor_bytes(a: bytes, b: bytes) -> bytes:
        """
        XOR two byte strings together.

        Args:
            a (bytes): The first byte string.
            b (bytes): The second byte string.

        Returns:
            bytes: The XORed byte string.
        """
        # Convert the byte strings to numpy arrays for efficient XOR operation
        arr_a = np.frombuffer(a, dtype=np.uint8)
        arr_b = np.frombuffer(b, dtype=np.uint8)
        return bytes(arr_a ^ arr_b)

    BSHSP_secret: bytes = obfuscated
    
    for i in reversed(range(layers)):

        # Get the pepper bytes from the obfuscated data
        pepper_masked_index = int(BSHSP_secret[-3:].decode("ascii")) # Extract pepper_masked_index from the obfuscated data
        pepper_index: int = pepper_masked_index % PEPPER_TUPLE_COUNT # Unmask the pepper index
        pepper: bytes = PEPPER_TUPLE[pepper_index].encode() # Get the pepper bytes from the tuple              

        # Hash the salt and pepper for the masked index number of times
        for _ in range(pepper_index+1):
            hash_salt: bytes = hashlib.sha256(salt).digest()
            hash_pepper: bytes = hashlib.sha256(pepper).digest()

        # Get the scheme and secret from the obfuscated data
        BSHSP_masked_scheme: int = int(BSHSP_secret[-6:-3].decode("ascii"))
        BSHSP_scheme: int = BSHSP_masked_scheme % BSHSP_SCHEMES # Unmask the BSHSP scheme
        secret: bytes = BSHSP_secret[:-6] # Remove the scheme and pepper index from the obfuscated data

        # Reverse step: xor first
        xor_base: bytes = (hash_salt + hash_pepper) * ((len(secret) // len(hash_salt + hash_pepper)) + 1)
        xored_bytes = xor_bytes(secret, xor_base[:len(secret)])

        # Then deobfuscate
        BSHSP_secret = deobfuscate(xored_bytes.decode("latin1"), BSHSP_scheme).encode("latin1")
        

    return BSHSP_secret
        
def main():
    
    mismatch = 0
    iterations = 1000
    for i in range(iterations):
        secret = secrets.token_urlsafe(random.randint(1, 10_000))
        secret_byte = secret.encode("latin1")
        salt = os.urandom(32)
        obfuscated = _BSHSP_obfuscation(secret_byte, salt)
        deobfuscated = _BSHSP_deobfuscation(obfuscated, salt)
        
        if deobfuscated != secret_byte:
            mismatch += 1


    print("Mismatch total count:", mismatch)
    print("Match %", (1 - mismatch / iterations) * 100)

if __name__ == "__main__":
    main()