{"schemaVersion": 2, "mediaType": "application/vnd.docker.distribution.manifest.v2+json", "config": {"mediaType": "application/vnd.docker.container.image.v1+json", "digest": "sha256:e75ac6f87f91bfc8618cc7ac470edbceb1982df00b9860363d286a1eadfb4d1c", "size": 264}, "layers": [{"mediaType": "application/vnd.ollama.image.model", "digest": "sha256:978f42df3b8df9b08f0deba1bb25d907b1bc50f1fcc8f7feca6629aab9b60a95", "size": 12119688320, "from": "/root/.ollama/models/blobs/sha256-978f42df3b8df9b08f0deba1bb25d907b1bc50f1fcc8f7feca6629aab9b60a95"}]}